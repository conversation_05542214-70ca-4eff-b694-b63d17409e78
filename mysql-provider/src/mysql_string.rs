use std::ops::Deref;
use std::fmt;
use sqlx::{
    mysql::{MySql, MySqlTypeInfo, MySqlValueRef},
    decode::Decode,
    encode::{Encode, IsNull},
    types::Type,
    error::BoxDynError,
};

use std::str::from_utf8;


impl fmt::Display for MySqlString {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.0)
    }
}

// 实现 Deref trait，使其可以直接解引用为 &str
// 这样 Option<MySqlString> 就可以直接使用 .as_deref()
impl Deref for MySqlString {
    type Target = str;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

#[derive(Debug, Clone, PartialEq, Eq)]
pub struct MySqlString(pub String);

impl Type<MySql> for MySqlString {
    fn type_info() -> MySqlTypeInfo {
        <&[u8] as Type<MySql>>::type_info()
    }

    fn compatible(ty: &MySqlTypeInfo) -> bool {
        <String as Type<MySql>>::compatible(ty) || <&[u8] as Type<MySql>>::compatible(ty)
    }
}


impl<'r> Decode<'r, MySql> for MySqlString {
    fn decode(value: MySqlValueRef<'r>) -> Result<Self, BoxDynError> {
        let bytes = <&[u8] as Decode<MySql>>::decode(value)?;
        let string = from_utf8(bytes)?.to_owned();
        Ok(MySqlString(string))
    }
}

impl<'q> Encode<'q, MySql> for MySqlString {
    fn encode_by_ref(&self, buf: &mut Vec<u8>) -> Result<IsNull, BoxDynError>  {
        <&str as Encode<MySql>>::encode_by_ref(&&self.0.as_ref(), buf)
    }
}
