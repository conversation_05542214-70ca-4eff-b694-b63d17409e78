pub mod mysql_string;

use anyhow::Result;
use async_trait::async_trait;
use sqlx::mysql::{MySqlConnectOptions, MySqlPoolOptions};
use sqlx::{FromRow, MySql, Pool};
use std::time::Duration;
use thiserror::Error;

// 定义MySQL连接错误
#[derive(Debug, Error)]
pub enum MySqlProviderError {
    #[error("数据库连接错误: {0}")]
    ConnectionError(#[from] sqlx::Error),
    #[error("SQL执行错误: {0}")]
    ExecutionError(String),
    #[error("数据解析错误: {0}")]
    ParseError(String),
}

// 定义MySQL提供程序配置
#[derive(Clone, Debug)]
pub struct MySqlConfig {
    pub host: String,
    pub port: u16,
    pub username: String,
    pub password: String,
    pub database: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub connection_timeout: Duration,
    pub max_lifetime: Duration,
    pub idle_timeout: Duration,
}

impl Default for MySqlConfig {
    fn default() -> Self {
        Self {
            host: "localhost".to_string(),
            port: 3306,
            username: "root".to_string(),
            password: "".to_string(),
            database: "test".to_string(),
            max_connections: 10,
            min_connections: 1,
            connection_timeout: Duration::from_secs(5),
            max_lifetime: Duration::from_secs(30 * 60),
            idle_timeout: Duration::from_secs(10 * 60),
        }
    }
}

// 定义MySQL提供程序接口
#[async_trait]
pub trait MySqlProvider {
    async fn query<T>(&self, query: &str) -> Result<Vec<T>, MySqlProviderError>
    where
        T: for<'r> FromRow<'r, sqlx::mysql::MySqlRow> + Send + Unpin;

    async fn query_with_param<T>(&self, query: &str, param: &str) -> Result<Vec<T>, MySqlProviderError>
    where
        T: for<'r> FromRow<'r, sqlx::mysql::MySqlRow> + Send + Unpin;

    async fn execute(&self, query: &str) -> Result<u64, MySqlProviderError>;

    async fn execute_with_param(&self, query: &str, param: &str) -> Result<u64, MySqlProviderError>;

    async fn execute_batch(&self, queries: &[String]) -> Result<(), MySqlProviderError>;

    async fn count(&self, query: &str) -> Result<Option<i64>, MySqlProviderError>;

    async fn count_with_param(&self, query: &str, param: &str) -> Result<Option<i64>, MySqlProviderError>;

    // 获取连接池引用
    fn get_pool(&self) -> &Pool<MySql>;
}

// MySQL提供程序实现
pub struct MySqlProviderImpl {
    pool: Pool<MySql>,
}

impl MySqlProviderImpl {
    // 创建新的MySQL连接池
    pub async fn new(config: MySqlConfig) -> Result<Self, MySqlProviderError> {
        let connect_options = MySqlConnectOptions::new()
            .host(&config.host)
            .port(config.port)
            .username(&config.username)
            .password(&config.password)
            .database(&config.database);

        let pool = MySqlPoolOptions::new()
            .max_connections(config.max_connections)
            .min_connections(config.min_connections)
            .acquire_timeout(config.connection_timeout)
            .max_lifetime(config.max_lifetime)
            .idle_timeout(config.idle_timeout)
            .connect_with(connect_options)
            .await?;

        Ok(Self { pool })
    }
}

#[async_trait]
impl MySqlProvider for MySqlProviderImpl {
    // 执行查询并返回结构体集合
    async fn query<T>(&self, query: &str) -> Result<Vec<T>, MySqlProviderError>
    where
        T: for<'r> FromRow<'r, sqlx::mysql::MySqlRow> + Send + Unpin,
    {
        let start = std::time::Instant::now();
        log::info!("读取mysql开始: {}", query);

        let result = sqlx::query_as::<_, T>(query)
            .fetch_all(&self.pool)
            .await
            .map_err(|e| MySqlProviderError::ExecutionError(e.to_string()))?;

        log::info!("读取mysql完成，耗时：{:?}, dataSize: {}", start.elapsed(), result.len());
        Ok(result)
    }

    // 执行带参数的查询并返回结构体集合
    async fn query_with_param<T>(&self, query: &str, param: &str) -> Result<Vec<T>, MySqlProviderError>
    where
        T: for<'r> FromRow<'r, sqlx::mysql::MySqlRow> + Send + Unpin,
    {
        let start = std::time::Instant::now();
        log::info!("带参数读取mysql开始: {}", query);

        let result = sqlx::query_as::<_, T>(query)
            .bind(param)
            .fetch_all(&self.pool)
            .await
            .map_err(|e| MySqlProviderError::ExecutionError(e.to_string()))?;

        log::info!("带参数读取mysql完成，耗时：{:?}, dataSize: {}", start.elapsed(), result.len());
        Ok(result)
    }

    // 执行更新、插入或删除操作
    async fn execute(&self, query: &str) -> Result<u64, MySqlProviderError> {
        let start = std::time::Instant::now();
        log::info!("执行mysql开始: {}", query);

        let result = sqlx::query(query)
            .execute(&self.pool)
            .await
            .map_err(|e| MySqlProviderError::ExecutionError(e.to_string()))?;

        let rows_affected = result.rows_affected();
        log::info!("执行mysql完成，耗时：{:?}, 影响行数: {}", start.elapsed(), rows_affected);
        Ok(rows_affected)
    }

    // 执行带参数的更新、插入或删除操作
    async fn execute_with_param(&self, query: &str, param: &str) -> Result<u64, MySqlProviderError> {
        let start = std::time::Instant::now();
        log::info!("带参数执行mysql开始: {}", query);

        let result = sqlx::query(query)
            .bind(param)
            .execute(&self.pool)
            .await
            .map_err(|e| MySqlProviderError::ExecutionError(e.to_string()))?;

        let rows_affected = result.rows_affected();
        log::info!("带参数执行mysql完成，耗时：{:?}, 影响行数: {}", start.elapsed(), rows_affected);
        Ok(rows_affected)
    }

    // 执行批量查询
    async fn execute_batch(&self, queries: &[String]) -> Result<(), MySqlProviderError> {
        let start = std::time::Instant::now();
        log::info!("事务执行mysql开始，总SQL数: {}", queries.len());

        let pool = &self.pool;
        let mut tx = pool
            .begin()
            .await
            .map_err(|e| MySqlProviderError::ExecutionError(e.to_string()))?;

        for (index, query) in queries.iter().enumerate() {
            let query_start = std::time::Instant::now();
            log::info!("准备执行第{}条SQL: {}", index + 1, query);

            sqlx::query(query)
                .execute(&mut *tx)
                .await
                .map_err(|e| MySqlProviderError::ExecutionError(e.to_string()))?;

            log::info!("结束执行第{}条SQL，耗时：{:?}", index + 1, query_start.elapsed());
        }

        tx.commit()
            .await
            .map_err(|e| MySqlProviderError::ExecutionError(e.to_string()))?;

        log::info!("事务执行mysql完成，总耗时：{:?}", start.elapsed());
        Ok(())
    }

    // 执行计数查询，返回查询结果的第一个值（通常是COUNT(*)的结果）
    async fn count(&self, query: &str) -> Result<Option<i64>, MySqlProviderError> {
        let start = std::time::Instant::now();
        log::info!("读取mysql开始: {}", query);

        // 查询单个值
        let result = sqlx::query_scalar::<_, i64>(query)
            .fetch_optional(&self.pool)
            .await
            .map_err(|e| MySqlProviderError::ExecutionError(e.to_string()))?;

        log::info!("读取mysql完成，耗时：{:?}, count = {:?}", start.elapsed(), result);
        Ok(result)
    }

    // 执行带参数的计数查询，返回查询结果的第一个值
    async fn count_with_param(&self, query: &str, param: &str) -> Result<Option<i64>, MySqlProviderError> {
        let start = std::time::Instant::now();
        log::info!("带参数读取mysql开始: {}", query);

        // 查询单个值，绑定参数
        let result = sqlx::query_scalar::<_, i64>(query)
            .bind(param)
            .fetch_optional(&self.pool)
            .await
            .map_err(|e| MySqlProviderError::ExecutionError(e.to_string()))?;

        log::info!("带参数读取mysql完成，耗时：{:?}, count = {:?}", start.elapsed(), result);
        Ok(result)
    }

    // 获取连接池引用
    fn get_pool(&self) -> &Pool<MySql> {
        &self.pool
    }
}
