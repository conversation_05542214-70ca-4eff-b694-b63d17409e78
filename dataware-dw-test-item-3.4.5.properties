# stdf
task.onedata.dataware.ods.cpResultDir=/user/glory/data/onedata/dataware/ods/result/{FILE_CATEGORY}/{TYPE}/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/WAFER_NO={WAFER_NO}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}
task.onedata.dataware.ods.ftResultDir=/user/glory/data/onedata/dataware/ods/result/{FILE_CATEGORY}/{TYPE}/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}
task.onedata.dataware.dwd.cpDieDetailResultDir=/user/glory/data/onedata/dataware/dwd/result/die_detail/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/WAFER_NO={WAFER_NO}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}
task.onedata.dataware.dwd.dieDetailResultDir=/user/glory/data/onedata/dataware/dwd/result/die_detail/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}
task.onedata.dataware.dwd.cpTestItemDetailResultDir=/user/glory/data/onedata/dataware/dwd/result/test_item_detail/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/WAFER_NO={WAFER_NO}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}
task.onedata.dataware.dwd.testItemDetailResultDir=/user/glory/data/onedata/dataware/dwd/result/test_item_detail/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}
task.onedata.dataware.dim.allDieDetailPath=/user/glory/data/onedata/dataware/dwd/result/die_detail/TEST_AREA={TEST_AREA},/user/glory/data/onedata/dataware/dwd_history/result/die_detail/TEST_AREA={TEST_AREA}
task.onedata.dataware.dim.allTestItemDetailPath=/user/glory/data/onedata/dataware/dwd/result/test_item_detail/TEST_AREA={TEST_AREA},/user/glory/data/onedata/dataware/dwd_history/result/test_item_detail/TEST_AREA={TEST_AREA}

task.onedata.dataware.dim.cpDimResultDirTemplate=/user/glory/data/onedata/dataware/dim/result/{TABLE}/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/WAFER_NO={WAFER_NO}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}
task.onedata.dataware.dim.ftDimResultDirTemplate=/user/glory/data/onedata/dataware/dim/result/{TABLE}/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}

task.onedata.dataware.dws.cpDwsResultDirTemplate=/user/glory/data/onedata/dataware/dws/result/{TABLE}/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/WAFER_NO={WAFER_NO}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}
task.onedata.dataware.dws.ftDwsResultDirTemplate=/user/glory/data/onedata/dataware/dws/result/{TABLE}/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}

#bitmem
task.onedata.dataware.dwd.cpDieBitmemDetailResultDir=/user/glory/data/onedata/dataware/dwd/result/die_bitmem_detail/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/WAFER_NO={WAFER_NO}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}
task.onedata.dataware.dwd.dieBitmemDetailResultDir=/user/glory/data/onedata/dataware/dwd/result/die_bitmem_detail/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}
task.onedata.dataware.dwd.cpTestItemBitmemDetailResultDir=/user/glory/data/onedata/dataware/dwd/result/test_item_bitmem_detail/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/WAFER_NO={WAFER_NO}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}
task.onedata.dataware.dwd.testItemBitmemDetailResultDir=/user/glory/data/onedata/dataware/dwd/result/test_item_bitmem_detail/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}

task.onedata.dataware.dwd.testItemDetailResultPartition=24
task.onedata.dataware.dwd.testItemBitmemDetailResultPartition=24
task.onedata.dataware.dwd.zstdMaxPartitionBytes=1048576
task.onedata.dataware.dim.dimResultPartition=1
task.onedata.dataware.dws.dwsResultPartition=1
# 注意！一旦dwd写入数据，lotBucketNum就不能再修改了
task.onedata.dataware.dwd.lotBucketNum=6
task.onedata.dataware.dwd.standardUnits=kHz:Hz;MHz:Hz;GHz:Hz;kV:V;mV:V;uV:V;nV:V;pV:V;fV:V;kA:A;mA:A;uA:A;nA:A;pA:A;fA:A;mF:F;uF:F;nF:F;pF:F;fF:F;aF:F;kOhm:Ohm;MOhm:Ohm;kW:W;mW:W;uW:W;nW:W;ms:s;us:s
task.onedata.dataware.dwd.dwdDbName=dwd
task.onedata.dataware.dim.dimDbName=dim
task.onedata.dataware.dws.dwsDbName=dws
task.onedata.dataware.ads.adsDbName=test
task.onedata.dataware.ods.odsDbName=ods
task.onedata.dataware.meta.metaDbName=meta
# ck
data.clickhouse.ckProtocol=TCP
data.clickhouse.ckAddress=*******************************************
data.clickhouse.ckUsername=admin
data.clickhouse.ckPassword=admin@ck@Guwave
data.clickhouse.ckBatchSize=400000
data.clickhouse.ckCluster=cluster_3shards_1replicas
data.clickhouse.dimNumPartitions=1
data.clickhouse.indexNumPartition=5
data.clickhouse.ckFetchSize=10000
data.clickhouse.ckNodeHost=dev.mpp01.guwave.com:8123
data.clickhouse.ckNodeUser=clickhouse
data.clickhouse.ckNodePassword=clickhouse@guwave
data.clickhouse.parquetBlockSize=10485760
data.clickhouse.totalMultiIfLimit=3000
data.clickhouse.ckDriver=com.github.housepower.jdbc.ClickHouseDriver
data.clickhouse.ckUrl=*******************************************/{DATABASE}
# mysql
data.mysql.address=************************************************
data.mysql.driver=com.mysql.cj.jdbc.Driver
data.mysql.username=bi
data.mysql.password=bi@guwave
data.mysql.fetchSize=10000
data.mysql.onedatadbname=onedata
# kafka
kafka.bootstrapServers=dev-gdp01.guwave.com:6667

# testValue/hiLimit/lowLimit是否需要乘以scale
settings.needMultiplyScaleCustomer=CUS_001,CUS_002,GUWAVE

# insert cluster table
insertClusterTable=true

# TEST_NUM设为0
settings.cpTestNumClearFlag=
settings.ftTestNumClearFlag=

redisAddress=redis://redis01.dev.guwave.com:6379
redisPassword=devops@guwave

# yms
task.onedata.yms.ads.ymsTestItemSiteBinPartition=1
task.onedata.yms.ads.ymsTestItemBinPartition=1
task.onedata.yms.ads.ymsTestItemSitePartition=1
task.onedata.yms.ads.ymsTestItemProgramPartition=1
task.onedata.yms.dwd.ymsDieDetailResultPartition=5
task.onedata.yms.dws.ymsWaferBinResultPartition=1

# 需要清洗test state=invalid数据的Customer
settings.needClearInvalidDataCustomer=XYH,GUBO1234456
rust.maxThreads=12
