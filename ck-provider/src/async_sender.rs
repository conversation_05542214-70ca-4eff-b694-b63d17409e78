use crate::config::StreamConfig;
use crate::error::CkProviderError;
use tokio::sync::mpsc;
use tokio::time::timeout;

/// Async sender for ClickHouse streaming operations with backpressure control
#[derive(Debug)]
pub struct AsyncCkSender<T> {
    sender: mpsc::Sender<Option<Vec<T>>>, // 改为批量发送
    config: StreamConfig,
    is_closed: std::sync::atomic::AtomicBool, // Individual close state per sender instance
}

impl<T> Clone for AsyncCkSender<T> {
    fn clone(&self) -> Self {
        Self {
            sender: self.sender.clone(),
            config: self.config.clone(),
            is_closed: std::sync::atomic::AtomicBool::new(false), // Each clone starts as not closed
        }
    }
}

impl<T> AsyncCkSender<T>
where
    T: Send + 'static,
{
    /// Create a new AsyncCkSender with the given channel sender
    pub fn new(sender: mpsc::Sender<Option<Vec<T>>>, config: StreamConfig) -> Self {
        Self { sender, config, is_closed: std::sync::atomic::AtomicBool::new(false) }
    }

    /// Send a single item with automatic backpressure handling (包装为单元素批次)
    pub async fn send(&self, item: Option<T>) -> Result<(), CkProviderError> {
        self.send_batch(item.map(|item| vec![item])).await
    }

    /// Send a single optional item with automatic backpressure handling
    pub async fn send_optional(&self, item: Option<T>) -> Result<(), CkProviderError> {
        if self.is_closed() {
            return Err(CkProviderError::StreamClosed("Sender is closed".to_string()));
        }

        let batch = match item {
            Some(item) => Some(vec![item]),
            None => None,
        };

        // Try to send with timeout for backpressure control
        let send_result = timeout(self.config.backpressure_timeout, self.sender.send(batch)).await;

        match send_result {
            Ok(Ok(())) => {
                // Successfully sent
                Ok(())
            }
            Ok(Err(_)) => {
                // Channel is closed
                self.mark_closed();
                Err(CkProviderError::StreamClosed("Channel is closed".to_string()))
            }
            Err(_) => {
                // Timeout occurred - backpressure
                Err(CkProviderError::BackpressureTimeout(format!(
                    "Send timeout after {:?}",
                    self.config.backpressure_timeout
                )))
            }
        }
    }

    /// Send multiple items in batch with optimized performance
    pub async fn send_batch(&self, items: Option<Vec<T>>) -> Result<(), CkProviderError> {
        if self.is_closed() {
            return Err(CkProviderError::StreamClosed("Sender is closed".to_string()));
        }

        // 直接发送整个批次，大幅简化逻辑和提升性能
        let send_result = timeout(self.config.backpressure_timeout, self.sender.send(items)).await;

        match send_result {
            Ok(Ok(())) => {
                // Successfully sent, continue with next item
            }
            Ok(Err(_)) => {
                // Channel is closed
                self.mark_closed();
                return Err(CkProviderError::StreamClosed("Channel is closed".to_string()));
            }
            Err(_) => {
                // Timeout occurred - backpressure
                return Err(CkProviderError::BackpressureTimeout(format!(
                    "Send timeout after {:?}",
                    self.config.backpressure_timeout
                )));
            }
        }

        Ok(())
    }

    /// Close the sender gracefully
    pub fn close(&self) {
        self.mark_closed();
        // Note: We don't close the actual sender here because other clones might still be using it
        // The sender will be closed when all clones are dropped
    }

    /// Close the sender and wait for all data to be processed
    /// This is a convenience method that closes the sender and returns immediately
    /// The caller should use the processor's wait_for_completion method to wait for processing to finish
    pub fn close_and_signal_completion(&self) {
        self.close();
        // The actual waiting should be done on the processor side
    }

    /// Check if the sender is closed
    pub fn is_closed(&self) -> bool {
        self.is_closed.load(std::sync::atomic::Ordering::Relaxed) || self.sender.is_closed()
    }

    /// Get the current capacity of the channel
    pub fn capacity(&self) -> usize {
        self.sender.capacity()
    }

    /// Get a reference to the configuration
    pub fn config(&self) -> &StreamConfig {
        &self.config
    }

    /// Mark the sender as closed
    fn mark_closed(&self) {
        self.is_closed.store(true, std::sync::atomic::Ordering::Relaxed);
    }
}

/// Factory for creating AsyncCkSender and receiver pairs
pub struct AsyncCkChannel;

impl AsyncCkChannel {
    /// Create a new async channel pair for ClickHouse streaming
    pub fn new<T>(config: StreamConfig, capacity: usize) -> (AsyncCkSender<T>, mpsc::Receiver<Option<Vec<T>>>)
    where
        T: Send + 'static,
    {
        let (sender, receiver) = mpsc::channel(capacity);
        let async_sender = AsyncCkSender::new(sender, config);
        (async_sender, receiver)
    }
}
