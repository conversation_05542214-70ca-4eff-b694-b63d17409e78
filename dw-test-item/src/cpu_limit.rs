use std::sync::OnceLock;

/// CPU限制配置 - 限制最大CPU使用率
static CPU_LIMIT_INIT: OnceLock<()> = OnceLock::new();

/// 初始化CPU限制配置
/// 必须在任何rayon操作之前调用
pub fn init_cpu_limit(max_threads: u8) {
    let max_threads= max_threads as usize;
    CPU_LIMIT_INIT.get_or_init(|| {
        // 设置环境变量，确保rayon使用指定的线程数
        std::env::set_var("RAYON_NUM_THREADS", max_threads.to_string());

        // 尝试配置全局线程池
        match rayon::ThreadPoolBuilder::new()
            .num_threads(max_threads)
            .build_global()
        {
            Ok(_) => {
                log::info!("成功配置Rayon全局线程池，限制线程数为: {}", max_threads);
            }
            Err(_) => {
                // 如果全局线程池已经初始化，记录当前状态
                let current_threads = rayon::current_num_threads();
                if current_threads <= max_threads {
                    log::info!("Rayon线程池已初始化，当前线程数: {} (在限制范围内)", current_threads);
                } else {
                    log::warn!("Rayon线程池已初始化，当前线程数: {} (超出限制 {})", current_threads, max_threads);
                    log::warn!("建议在程序启动时更早调用 init_cpu_limit()");
                }
            }
        }

        log::info!("CPU限制初始化完成");
    });
}

/// 获取当前rayon线程数
pub fn get_current_thread_count() -> usize {
    rayon::current_num_threads()
}
