use std::collections::HashMap;
use common::model::constant::dw_layer::DwLayer;
use mysql_provider::MySqlConfig;
use common::model::dw_table_enum::{DwTableEnum};
use common::model::dw_table_calculate_step::DwTableCalculateStep;
use common::model::table_calculate_info::TableCalculateInfo;
use common::service::dw_table_calculate_record_service::{DwTableCalculateRecordService};
use common::model::constant::upload_type::UploadType;

/// Record Table Service for tracking table calculation timing
/// Based on tableCalculateRecordService usage patterns in CpDwsTestItemService.scala
pub struct RecordTableService {
    table_calculate_record_service: DwTableCalculateRecordService,
    calculate_time_map: HashMap<DwTableEnum, TableCalculateInfo>,
}

impl RecordTableService {
    /// Create new record table service instance
    pub fn new(upload_type: UploadType, file_id: Option<i64>, file_name: String) -> Self {
        Self {
            table_calculate_record_service: DwTableCalculateRecordService::new(upload_type, file_id, file_name),
            calculate_time_map: HashMap::new(),
        }
    }

    /// Create service with default values
    pub fn default() -> Self {
        Self {
            table_calculate_record_service: DwTableCalculateRecordService::default(),
            calculate_time_map: HashMap::new(),
        }
    }

    /// Start table calculation timing
    /// Corresponds to: tableCalculateRecordService.updateDwTableCalculate(calculateTimeMap, tableEnum, CALCULATE_START, dbName)
    pub fn start_calculate(&mut self, table_enum: DwTableEnum, db_name: Option<String>) {
        self.table_calculate_record_service.update_dw_table_calculate(
            &mut self.calculate_time_map,
            table_enum,
            DwTableCalculateStep::CalculateStart,
            db_name,
        );
    }

    /// End table calculation timing
    /// Corresponds to: tableCalculateRecordService.updateDwTableCalculate(calculateTimeMap, tableEnum, CALCULATE_END, dbName)
    pub fn end_calculate(&mut self, table_enum: DwTableEnum, db_name: Option<String>) {
        self.table_calculate_record_service.update_dw_table_calculate(
            &mut self.calculate_time_map,
            table_enum,
            DwTableCalculateStep::CalculateEnd,
            db_name,
        );
    }

    /// Start sink timing
    /// Corresponds to: tableCalculateRecordService.updateDwTableCalculate(calculateTimeMap, tableEnum, SINK_CK_START, dbName)
    pub fn start_sink(&mut self, table_enum: DwTableEnum, db_name: Option<String>) {
        self.table_calculate_record_service.update_dw_table_calculate(
            &mut self.calculate_time_map,
            table_enum,
            DwTableCalculateStep::SinkCkStart,
            db_name,
        );
    }

    /// End sink timing
    /// Corresponds to: tableCalculateRecordService.updateDwTableCalculate(calculateTimeMap, tableEnum, SINK_CK_END, dbName)
    pub fn end_sink(&mut self, table_enum: DwTableEnum, db_name: Option<String>) {
        self.table_calculate_record_service.update_dw_table_calculate(
            &mut self.calculate_time_map,
            table_enum,
            DwTableCalculateStep::SinkCkEnd,
            db_name,
        );
    }

    /// Save all table calculate records to database
    /// Corresponds to: tableCalculateRecordService.saveDwTableCalculate(...)
    pub async fn save_table_calculate_records(
        &self,
        customer: &str,
        sub_customer: &str,
        factory: &str,
        factory_site: &str,
        test_area: &str,
        device_id: &str,
        lot_id: &str,
        wafer_no: &str,
        file_category: &str,
        lot_type: &str,
        test_stage: &str,
        execute_mode: &str,
        mysql_config: MySqlConfig,
        dw_layer: DwLayer,
        run_mode: &str,
        upload_type: Option<UploadType>,
    ) -> Result<(), mysql_provider::MySqlProviderError> {
        self.table_calculate_record_service.save_dw_table_calculate(
            customer,
            sub_customer,
            factory,
            factory_site,
            test_area,
            device_id,
            lot_id,
            wafer_no,
            file_category,
            lot_type,
            test_stage,
            execute_mode,
            mysql_config,
            &self.calculate_time_map,
            dw_layer,
            run_mode,
            upload_type,
        ).await
    }

    /// Get reference to calculate time map for inspection
    pub fn get_calculate_time_map(&self) -> &HashMap<DwTableEnum, TableCalculateInfo> {
        &self.calculate_time_map
    }

    /// Clear all timing records
    pub fn clear_records(&mut self) {
        self.calculate_time_map.clear();
    }

    /// Check if any records exist
    pub fn has_records(&self) -> bool {
        !self.calculate_time_map.is_empty()
    }
}

/// Convenience macros for common table calculation patterns
/// Based on the repetitive patterns in CpDwsTestItemService.scala

/// Record calculation timing for a table operation
/// Usage: record_table_calculation!(service, DwTableEnum::DwsBinTestItemIndex, "db_name", {
///     // Your calculation logic here
/// });
#[macro_export]
macro_rules! record_table_calculation {
    ($service:expr, $table_enum:expr, $db_name:expr, $calculation_block:block) => {{
        $service.start_calculate($table_enum, Some($db_name.to_string()));
        let result = $calculation_block;
        $service.end_calculate($table_enum, Some($db_name.to_string()));
        result
    }};
}

/// Record calculation and sink timing for a table operation
/// Usage: record_table_calculation_with_sink!(service, DwTableEnum::DwsBinTestItemIndex, "db_name", {
///     // Your calculation logic here
/// }, {
///     // Your sink logic here  
/// });
#[macro_export]
macro_rules! record_table_calculation_with_sink {
    ($service:expr, $table_enum:expr, $db_name:expr, $calculation_block:block, $sink_block:block) => {{
        // Start calculation timing
        $service.start_calculate($table_enum, Some($db_name.to_string()));
        
        // Execute calculation
        let calculation_result = $calculation_block;
        
        // End calculation, start sink timing
        $service.end_calculate($table_enum, Some($db_name.to_string()));
        $service.start_sink($table_enum, Some($db_name.to_string()));
        
        // Execute sink operation
        let sink_result = $sink_block;
        
        // End sink timing
        $service.end_sink($table_enum, Some($db_name.to_string()));
        
        (calculation_result, sink_result)
    }};
}

/// Example usage patterns based on CpDwsTestItemService.scala
#[cfg(test)]
mod examples {
    use super::*;
    
    /// Example showing how to use the service similar to the Scala patterns
    pub async fn example_usage() {
        let mut record_service = RecordTableService::default();
        
        // Example 1: Simple calculation timing (like DIM_TEST_PROGRAM_TEST_ORDER)
        record_service.start_calculate(DwTableEnum::DimTestProgramTestOrder, Some("dim_db".to_string()));
        record_service.start_sink(DwTableEnum::DimTestProgramTestOrder, Some("dim_db".to_string()));
        
        // Your calculation logic would go here
        // new TestProgramTestOrderService(testArea).calculate(...)
        
        record_service.end_calculate(DwTableEnum::DimTestProgramTestOrder, Some("dim_db".to_string()));
        record_service.end_sink(DwTableEnum::DimTestProgramTestOrder, Some("dim_db".to_string()));
        
        // Example 2: Using the macro for cleaner code
        let _result = record_table_calculation_with_sink!(
            record_service,
            DwTableEnum::DwsBinTestItemIndex,
            "dws_db",
            {
                // Calculation logic
                println!("Calculating bin test item index...");
                // Return calculation result
                "calculation_result"
            },
            {
                // Sink logic
                println!("Writing to ClickHouse...");
                // Return sink result
                "sink_result"
            }
        );
        
        // Example 3: Save records at the end (like in the finally block)
        // if record_service.has_records() {
        //     let mysql_config = MySqlConfig::new(...);
        //     record_service.save_table_calculate_records(
        //         "customer", "sub_customer", "factory", "factory_site",
        //         "test_area", "device_id", "lot_id", "wafer_no",
        //         "file_category", "lot_type", "test_stage", "execute_mode",
        //         mysql_config, DwLayer::DWS, "run_mode", None
        //     ).await.expect("Failed to save records");
        // }
    }
}