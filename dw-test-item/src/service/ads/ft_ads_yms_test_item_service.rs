use crate::config::DwTestItemConfig;
use crate::service::ads::{execute_tombstone_operations, AdsYmsTestItemService};
use chrono::Utc;
use ck_provider::{Ck<PERSON>rovider, CkProviderImpl};
use common::ads::sink::test_item_bin_handler::TestItemBinHandler;
use common::ads::sink::test_item_program_handler::TestItemProgramHandler;
use common::ads::sink::test_item_site_bin_handler::TestItemSiteBinHandler;
use common::ads::sink::test_item_site_handler::TestItemSiteHandler;
use common::ck::ck_sink::SinkHandler;
use common::dto::ads::value::die_final_info::DieFinalInfo;
use common::dto::dwd::file_detail::FileDetail;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::dto::ods::product_config::OdsProductConfig;
use common::model::constant::test_area::TestArea;
use common::model::constant::*;
use mysql_provider::{MySqlProvider, MySqlProviderImpl};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use std::collections::HashMap;
use std::error::Error;
use std::time::{SystemTime, UNIX_EPOCH};

/// Version flag structure for MySQL query result
/// Corresponds to the VersionFlag case class in Scala
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct VersionFlag {
    pub version_flag: bool,
}

/// FT YMS ADS Test Item Service
/// Handles FT (Final Test) stage ADS layer test item processing for YMS
///
/// Corresponds to: FtYmsAdsTestItemService.scala
/// case class FtYmsAdsTestItemService(properties: DwTestItemProperties) extends YmsAdsTestItemCommonService(properties)
#[derive(Debug, Clone)]
pub struct FtYmsAdsTestItemService {
    /// Configuration properties for the service
    properties: DwTestItemConfig,
}

impl FtYmsAdsTestItemService {
    /// Get product configuration SQL query
    ///
    /// Corresponds to: GET_PRODUCT_SQL in FtYmsAdsTestItemService.scala
    fn get_product_sql(&self) -> String {
        r#"
        SELECT DISTINCT DATA_SOURCE
               ,CUSTOMER
               ,SUB_CUSTOMER
               ,FACTORY
               ,FACTORY_SITE
               ,TEST_AREA
               ,TEST_STAGE
               ,DEVICE_ID
               ,PRODUCT
               ,PRODUCT_TYPE
               ,PRODUCT_FAMILY
        FROM {ODS_DB_NAME}.ods_yms_wafermap_config_snapshot_cluster
        WHERE DATA_SOURCE = '{DATA_SOURCE}'
          AND CUSTOMER = '{CUSTOMER}'
          AND FACTORY = '{FACTORY}'
          AND TEST_AREA = '{TEST_AREA}'
          AND TEST_STAGE = '{TEST_STAGE}'
          AND DEVICE_ID = '{DEVICE_ID}'
          AND DT = (SELECT LATEST_PARTITION_VALUE
                    FROM {META_DB_NAME}.meta_table_latest_partition_cluster
                    WHERE TABLE_NAME = 'ods_yms_wafermap_config_snapshot_cluster'
                    AND DATABASE_NAME = '{ODS_DB_NAME}')
        "#
        .to_string()
    }

    /// Get die final information SQL query
    ///
    /// Corresponds to: GET_DIE_FINAL_INFO_SQL in FtYmsAdsTestItemService.scala
    /// Note: FT uses different test areas compared to CP
    fn get_die_final_info_sql(&self) -> String {
        r#"
        SELECT DISTINCT
         CUSTOMER
        ,FACTORY
        ,TEST_AREA
        ,DEVICE_ID
        ,LOT_TYPE
        ,TEST_STAGE
        ,LOT_ID
        ,WAFER_NO
        ,ECID
        ,C_PART_ID
        ,IS_FINAL_TEST_IGNORE_TP
        FROM {DWD_DB_NAME}.dwd_die_detail_cluster
        WHERE IS_DELETE = 0
          AND TEST_AREA IN ('ASSY', 'REL', 'FT', 'SLT', 'MT', 'NA', 'CAL')
          AND CUSTOMER = '{CUSTOMER}'
          AND FACTORY = '{FACTORY}'
          AND TEST_AREA = '{TEST_AREA}'
          AND DEVICE_ID = '{DEVICE_ID}'
          AND LOT_TYPE = '{LOT_TYPE}'
          AND TEST_STAGE = '{TEST_STAGE}'
          AND LOT_ID = '{LOT_ID}'
        "#.to_string()
    }

    /// Get version information SQL query
    ///
    /// Corresponds to: GET_VERSION_INFO_SQL in FtYmsAdsTestItemService.scala
    /// Note: FT version query does not include wafer_no in partition (lot-level processing)
    fn get_version_info_sql(&self) -> String {
        r#"
       SELECT  CASE WHEN latest_version IN ('1.11.0','1.12.0','1.12.1','1.12.2','1.12.3','1.13.0','2.0.0','2.1.0','2.1.1','2.2.0','2.3.0','2.4.0','2.4.1','2.5.0','2.6.0','2.7.0','2.7.1','2.8.0','2.9.0','2.9.1','2.9.2') or latest_version is null THEN false ELSE true END AS version_flag
       FROM
       (
           SELECT  first_value(version) over(PARTITION BY customer,factory,test_area,device_id,lot_type,test_stage,lot_id ORDER BY  update_time DESC) AS latest_version
           FROM {ONEDATA_DB_NAME}.dw_layer_calculate_pool
           WHERE process_status = 'SUCCESS'
           AND dw_layer = 'DWD'
           AND customer = '{CUSTOMER}'
           AND factory = '{FACTORY}'
           AND test_area = '{TEST_AREA}'
           AND device_id = '{DEVICE_ID}'
           AND lot_type = '{LOT_TYPE}'
           AND test_stage = '{TEST_STAGE}'
           AND lot_id = '{LOT_ID}'
       ) t
       "#.to_string()
    }

    /// Create new FtYmsAdsTestItemService
    ///
    /// Corresponds to: FtYmsAdsTestItemService.scala case class constructor
    pub fn new(properties: DwTestItemConfig) -> Self {
        Self { properties }
    }

    /// Calculate FT YMS ADS test item processing
    ///
    /// Corresponds to: FtYmsAdsTestItemService.scala:calculate method
    /// def calculate(spark: SparkSession, fileDetailMap: Broadcast[Map[java.lang.Long, FileDetail]],
    ///              SubTestItemDetail: Dataset[SubTestItemDetail], customer: String, factory: String,
    ///              testArea: String, deviceId: String, lotType: String, testStage: String,
    ///              lotId: String, waferNo: String, dataVersion: String, fileCategory: String): Unit
    pub async fn calculate(
        &self,
        file_detail_map: &HashMap<i64, FileDetail>,
        sub_test_item_detail: &Vec<Vec<SubTestItemDetail>>,
        customer: &str,
        factory: &str,
        test_area: &str,
        device_id: &str,
        lot_type: &str,
        test_stage: &str,
        lot_id: &str,
        wafer_no: &str,
        _data_version: &str,
        _file_category: &str,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        // FT工艺的data_source逻辑：FT工艺通常使用TEST_RAW_DATA
        // 基于代码分析，FT工艺不像CP有复杂的Map数据源判断
        let data_source = TEST_RAW_DATA;

        let onedata_db_name = &self.properties.onedatadbname;

        // Build version SQL query - FT不包含wafer_no
        let version_sql = self
            .get_version_info_sql()
            .replace(CUSTOMER, customer)
            .replace(FACTORY, factory)
            .replace(TEST_AREA, test_area)
            .replace(DEVICE_ID, device_id)
            .replace(LOT_TYPE, lot_type)
            .replace(TEST_STAGE, test_stage)
            .replace(LOT_ID, lot_id)
            .replace("{ONEDATA_DB_NAME}", onedata_db_name);

        let mysql_config = self.properties.get_mysql_config();
        let mysql_provider = MySqlProviderImpl::new(mysql_config).await?;

        let version_flag = mysql_provider
            .query::<VersionFlag>(&version_sql)
            .await?
            .first()
            .map(|v| v.version_flag)
            .unwrap_or(false);

        let handlers: Vec<Box<dyn SinkHandler>> = vec![
            Box::new(TestItemSiteBinHandler::new(self.properties.ads_db_name.clone())),
            Box::new(TestItemBinHandler::new(self.properties.ads_db_name.clone())),
            Box::new(TestItemSiteHandler::new(self.properties.ads_db_name.clone())),
            Box::new(TestItemProgramHandler::new(self.properties.ads_db_name.clone())),
        ];

        execute_tombstone_operations(
            &self.properties,
            &handlers,
            customer,
            factory,
            test_area,
            lot_id,
            lot_type,
            test_stage,
            device_id,
            wafer_no,
            data_source,
            Utc::now(),
        )
        .await?;

        // Create ClickHouse configuration
        let ck_config = self.properties.get_ck_config(self.properties.ads_db_name.as_str());
        let ck_provider = CkProviderImpl::new(ck_config.clone());

        let product_sql = self
            .get_product_sql()
            .replace(ODS_DB_NAME, &self.properties.ods_db_name)
            .replace(META_DB_NAME, &self.properties.meta_db_name)
            .replace(DATA_SOURCE, &data_source)
            .replace(CUSTOMER, customer)
            .replace(FACTORY, factory)
            .replace(TEST_AREA, test_area)
            .replace(TEST_STAGE, test_stage)
            .replace(DEVICE_ID, device_id);

        let product_infos = ck_provider.query::<OdsProductConfig>(&product_sql).await?;

        // FT的die_final_sql不包含WAFER_NO条件（lot级别处理）
        let die_final_sql = self
            .get_die_final_info_sql()
            .replace(DWD_DB_NAME, self.properties.dwd_db_name.as_str())
            .replace(CUSTOMER, customer)
            .replace(FACTORY, factory)
            .replace(TEST_AREA, test_area)
            .replace(DEVICE_ID, device_id)
            .replace(LOT_TYPE, lot_type)
            .replace(TEST_STAGE, test_stage)
            .replace(LOT_ID, lot_id);

        let die_final_infos: Vec<DieFinalInfo> = ck_provider.query::<DieFinalInfo>(&die_final_sql).await?;

        // Get current timestamp as version
        let version = SystemTime::now().duration_since(UNIX_EPOCH)?.as_millis() as i64;

        // Create ADS YMS Test Item Service
        AdsYmsTestItemService::new(
            self.properties.clone(),
            data_source.to_owned(),
            TestArea::of(test_area)
                .ok_or_else(|| panic!("Unknown test area: {}", test_area))
                .unwrap(),
        )
        .calculate_test_item(
            &sub_test_item_detail,
            &file_detail_map,
            &product_infos,
            &die_final_infos,
            version,
            version_flag,
        )
        .await?;

        Ok(())
    }
}
