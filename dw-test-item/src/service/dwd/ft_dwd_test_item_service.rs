use crate::config::DwTestItemConfig;

use common::dto::dwd::die_detail_parquet::DieDetailParquet;
use common::dto::dwd::file_detail::FileDetail;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::dto::ods::test_item_data_parquet::TestItemDataParquet;
use common::dwd::dwd_service::DwdService;
use common::dwd::model::value::die_test_info::DieTestInfo;
use common::dwd::table::distributed::test_item_detail_service::TestItemDetailService;
use common::model::constant::upload_type::UploadType;
use common::model::key::{die_key::DieKey, lot_key::LotKey, wafer_key::WaferKey};
use common::repository::mysql::test_num_force_zero_config_repository::TestNumForceZeroConfigRepository;
use common::utils::path;
use mysql_provider::{MySqlConfig, MySqlProviderImpl};
use parquet_provider::hdfs_provider::HdfsConfig;
use parquet_provider::parquet_provider::write_parquet_multi;

use std::collections::HashMap;
use std::error::Error;
use std::sync::Arc;
use rayon::prelude::*;
use crate::service::dwd::dwd_service;
// Corresponding to Scala file:
// /dataware/dataware-dw/dataware-dw-test-item/src/main/scala/com/guwave/onedata/dataware/dw/testItem/spark/dwd/service/impl/FtDwdTestItemService.scala

/// FtDwdTestItemService handles FT (Final Test) stage DWD layer test item processing
/// This service orchestrates the calculation of test item details for the FT test stage
#[derive(Debug, Clone)]
pub struct FtDwdTestItemService {
    test_item_detail_result_partition: i32,
    test_item_detail_service: TestItemDetailService,
    mysql_config: MySqlConfig,
    properties: DwTestItemConfig,
}

/// Calculation result containing broadcast maps and datasets
/// Corresponds to the return type of calculate method in Scala (line 30)
#[derive(Debug)]
pub struct FtDwdCalculationResult {
    pub file_detail_map: HashMap<i64, FileDetail>,
    pub die_detail: Vec<DieDetailParquet>,
    pub test_item_detail: Vec<Vec<SubTestItemDetail>>,
}

impl FtDwdTestItemService {
    /// Create new FtDwdTestItemService
    ///
    /// Corresponds to: FtDwdTestItemService.scala:27 (case class constructor)
    /// case class FtDwdTestItemService(properties: DwTestItemProperties, testItemDetailResultPartition: Int)
    pub fn new(test_item_detail_result_partition: i32, test_area: String) -> Self {
        let config: DwTestItemConfig = DwTestItemConfig::get_config().unwrap();
        Self {
            test_item_detail_result_partition,
            test_item_detail_service: TestItemDetailService::new(test_area),
            mysql_config: config.get_mysql_config(),
            properties: config,
        }
    }

    /// Calculate FT DWD test item details
    ///
    /// Corresponds to: FtDwdTestItemService.scala:30-90
    /// def calculate(spark: SparkSession, dieDetailSource: Dataset[DieDetail], testItemData: Dataset[TestItemData],
    ///              waferKey: WaferKey, testArea: String, executeMode: String, fileCategory: String,
    ///              ckSinkType: String, runMode: String): (Broadcast[Map[lang.Long, FileDetail]], Dataset[DieDetail], Dataset[SubTestItemDetail])
    pub async fn calculate(
        &self,
        die_detail_source: Vec<DieDetailParquet>,
        test_item_data: Vec<Vec<TestItemDataParquet>>,
        wafer_key: &WaferKey,
        test_area: String,
        _execute_mode: String,
        file_category: String,
        _ck_sink_type: String,
        _run_mode: String,
        config: &DwTestItemConfig,
        mysql_provider: &MySqlProviderImpl
    ) -> Result<FtDwdCalculationResult, Box<dyn Error + Send + Sync>> {
        let lot_key = LotKey::new(
            wafer_key.customer.clone(),
            wafer_key.sub_customer.clone(),
            wafer_key.factory.clone(),
            wafer_key.factory_site.clone(),
            wafer_key.device_id.clone(),
            wafer_key.lot_id.clone(),
            wafer_key.test_area.clone(),
            wafer_key.test_stage.clone(),
            wafer_key.lot_type.clone(),
        );
        log::info!("当前正在计算的lot信息: {}", lot_key);
        let die_detail = die_detail_source;
        let test_num_force_zero_test_program_list = TestNumForceZeroConfigRepository::new(mysql_provider)
            .await?
            .read_test_num_force_zero_test_program_list(
                wafer_key.customer.clone(),
                wafer_key.sub_customer.clone(),
                wafer_key.factory.clone(),
                wafer_key.factory_site.clone(),
                test_area.to_string(),
                wafer_key.device_id.clone(),
                wafer_key.test_stage.clone(),
                die_detail
                    .iter()
                    .map(|die| die.TEST_PROGRAM.clone().unwrap())
                    .collect::<Vec<String>>(),
                UploadType::AUTO.to_string(),
            )
            .await?;

        let die_test_info_map = Arc::new(self.build_die_test_info_broadcast_map(&die_detail)?);
        let file_detail_map = self.build_file_detail_broadcast_map(&die_detail)?;

        // 使用rayon并行处理每个Vec<TestItemDataParquet>
        let service = Arc::new(self.test_item_detail_service.clone());
        let file_category = Arc::new(file_category.clone());
        let need_multiply_scale = self.properties.get_need_multiply_scale(wafer_key.customer.as_str());
        let standard_units = Arc::new(self.properties.standard_units.clone());
        let need_clear_invalid_data = self.properties.get_clear_invalid_data_flag(wafer_key.customer.as_str());
        let test_num_force_zero_list = Arc::new(test_num_force_zero_test_program_list.clone());

        // 使用rayon并行处理每个Vec<TestItemDataParquet>
        let test_item_detail: Result<Vec<Vec<SubTestItemDetail>>, Box<dyn Error + Send + Sync>> = test_item_data
            .into_par_iter()
            .enumerate()
            .map(|(index, data_batch)| {
                log::info!("开始处理第{}批数据，数据量: {}", index, data_batch.len());
                // 去掉重复文件的SubTestItemDetail (corresponds to Scala line 47-48)
                let test_item_detail_source = service
                    .calculate_ft_test_item_detail(
                        data_batch,
                        &file_category,
                        &die_test_info_map,
                        need_multiply_scale,
                        &standard_units,
                        need_clear_invalid_data,
                    )
                    .map_err(|e| -> Box<dyn Error + Send + Sync> {
                        Box::new(std::io::Error::new(
                            std::io::ErrorKind::Other,
                            format!("批次{}处理失败: {}", index, e),
                        ))
                    })?;

                // 计算TestItemDetail (corresponds to Scala line 51-52)
                // testItemDetailSource.map(testItemDetailService.fillDieTestInfo(_, dieTestInfoMap.value, testNumForceZeroTestProgramList.value))
                let result: Result<Vec<SubTestItemDetail>, Box<dyn Error + Send + Sync>> = test_item_detail_source
                    .into_iter()
                    .map(|sub_test_item_detail| {
                        service
                            .fill_die_test_info(
                                sub_test_item_detail,
                                &die_test_info_map,
                                &test_num_force_zero_list,
                            )
                            .map_err(|e| -> Box<dyn Error + Send + Sync> {
                                Box::new(std::io::Error::new(
                                    std::io::ErrorKind::Other,
                                    format!("填充die测试信息失败: {}", e),
                                ))
                            })
                    })
                    .collect();

                log::info!("完成处理第{}批数据", index);
                result
            })
            .collect();

        let test_item_detail = test_item_detail?;

        // test_item_detail is already Vec<Vec<SubTestItemDetail>>

        log::info!("test_item_detail length: {:?}", test_item_detail.len());

        let test_item_detail_path = path::get_dwd_lot_path(
            &self.properties.test_item_detail_result_dir,
            &test_area,
            &lot_key.customer,
            &lot_key.factory,
            &lot_key.lot_id,
            &lot_key.device_id,
            &lot_key.test_stage,
            &lot_key.lot_type,
        );

        log::info!("写入parquet文件到路径: {}", test_item_detail_path);

        let test_item_detail_refs = test_item_detail.iter().map(|a| a).collect();
        write_parquet_multi(
            &test_item_detail_path,
            &test_item_detail_refs,
            Some(&HdfsConfig::default()),
            config.get_batch_size()?,
        )
        .await?;
        log::info!("成功写入parquet文件到路径: {}", test_item_detail_path);

        // 并发写入每个批次到ClickHouse
        let (test_item_detail, file_detail_map) =
            dwd_service::write_test_item_detail_to_clickhouse_concurrent(
                self.properties.clone(),
                test_item_detail,
                file_detail_map,
                config,
            )
            .await?;

        Ok(FtDwdCalculationResult { file_detail_map, die_detail, test_item_detail })
    }

    /// Build die test info map from die details for broadcasting
    /// This method creates the broadcast map used in the calculation
    ///
    /// Corresponds to: FtDwdTestItemService.scala:43
    /// val dieTestInfoMap = sc.broadcast(dieDetail.map(buildDieTestInfo).rdd.distinct().collect.toMap)
    fn build_die_test_info_broadcast_map(
        &self,
        die_details: &[DieDetailParquet],
    ) -> Result<HashMap<DieKey, DieTestInfo>, Box<dyn Error + Send + Sync>> {
        DwdService::build_die_test_info_broadcast_map(die_details).map_err(|e| {
            Box::new(std::io::Error::new(std::io::ErrorKind::Other, e.to_string())) as Box<dyn Error + Send + Sync>
        })
    }

    /// Build file detail broadcast map from die details using FileDetailService
    /// This method creates the file detail map used for merging with test item details
    ///
    /// Corresponds to: FtDwdTestItemService.scala:45
    /// val fileDetailMap = FileDetailService().broadcastFileDetail(spark, dieDetail)
    fn build_file_detail_broadcast_map(
        &self,
        die_details: &[DieDetailParquet],
    ) -> Result<HashMap<i64, FileDetail>, Box<dyn Error + Send + Sync>> {
        let file_detail_service = common::dto::dwd::file_detail::FileDetailService::new();
        file_detail_service.broadcast_file_detail(die_details).map_err(|e| {
            Box::new(std::io::Error::new(std::io::ErrorKind::Other, e.to_string())) as Box<dyn Error + Send + Sync>
        })
    }

    /// Get test item detail result partition count
    pub fn get_test_item_detail_result_partition(&self) -> i32 {
        self.test_item_detail_result_partition
    }
}
