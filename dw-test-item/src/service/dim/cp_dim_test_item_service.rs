use crate::config::DwTestItemConfig;
use crate::service::dim::test_item_service::TestItemService;
use crate::service::dim::test_program_test_item_service::TestProgramTestItemService;
use crate::service::dim::{get_test_program_partition, write_to_ck_by_partition, write_to_clickhouse_after_tombstone};
use common::dim::sink::{TestItemHandler, TestProgramTestItemHandler};
use common::dto::dwd::file_detail::FileDetail;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::model::key::WaferKey;
use std::collections::HashMap;
use std::error::Error;

/// CpDimTestItemService handles CP (Contact Probe) stage DIM layer processing
/// Corresponds to CpDimTestItemService.scala
#[derive(Debug, <PERSON>lone)]
pub struct CpDimTestItemService {
    properties: DwTestItemConfig,
}

impl CpDimTestItemService {
    pub fn new(properties: DwTestItemConfig) -> Self {
        Self { properties }
    }

    /// Calculate CP DIM layer data
    /// Corresponds to calculate method in CpDimTestItemService.scala
    pub async fn calculate(
        &self,
        sub_test_item: &Vec<Vec<SubTestItemDetail>>,
        file_detail_map: &HashMap<i64, FileDetail>,
        wafer_key: WaferKey,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        log::info!("开始计算 CP DIM 层数据...");

        let dim_db_name = &self.properties.dim_db_name;

        // 1. 计算 DIM TestItem
        log::info!("开始计算 DIM TestItem...");
        let test_item_service = TestItemService::new(self.properties.clone());
        let test_items = test_item_service.calculate_test_item(sub_test_item, file_detail_map).await?;
        log::info!("DIM TestItem 计算完成，共生成 {} 条记录", test_items.len());

        let test_item_handler = TestItemHandler::new(dim_db_name.to_string(), self.properties.insert_cluster_table);
        write_to_clickhouse_after_tombstone(test_item_handler, &self.properties, &test_items, &wafer_key).await?;

        log::info!("开始计算 DIM TestProgramTestItem...");
        let test_program_test_item_service = TestProgramTestItemService::new(self.properties.clone());
        let test_program_test_item = test_program_test_item_service
            .calculate_test_program_test_item_from_test_item(&test_items, dim_db_name)
            .await?;
        log::info!("DIM TestProgramTestItem 计算完成");
        let test_program_test_item_handler = TestProgramTestItemHandler::new(dim_db_name.to_string());
        let partition = get_test_program_partition(
            wafer_key.customer.as_str(),
            wafer_key.test_area.as_str(),
            wafer_key.factory.as_str(),
        );
        write_to_ck_by_partition(&self.properties, &test_program_test_item, test_program_test_item_handler, partition.as_str())
            .await?;
        log::info!("DIM TestProgramTestItem 写入clickhouse完成");
        log::info!("CP DIM 层数据计算完成");
        Ok(())
    }
}
