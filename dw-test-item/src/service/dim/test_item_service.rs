use dashmap::DashMap;
use rayon::prelude::*;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};

use crate::config::DwTestItemConfig;
use common::dim::sink::test_item_handler::TestItemHandler;
use common::dto::dim::{DimTestItemRow, TestItemKey};
use common::dto::dwd::file_detail::FileDetail;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::utils::date;

/// TestItemService handles DIM layer TestItem processing
/// Corresponds to TestItemService.scala
#[derive(Debug, Clone)]
pub struct TestItemService {
    properties: DwTestItemConfig,
}

impl TestItemService {
    pub fn new(properties: DwTestItemConfig) -> Self {
        Self { properties }
    }

    /// Calculate TestItem from SubTestItemDetail
    /// Corresponds to calculateTestItem method in Scala
    pub async fn calculate_test_item(
        &self,
        sub_test_item: &Vec<Vec<SubTestItemDetail>>,
        file_detail_map: &HashMap<i64, FileDetail>,
    ) -> Result<Vec<DimTestItemRow>, Box<dyn std::error::Error + Send + Sync>> {
        log::info!("开始计算 DIM TestItem...");

        // Transform SubTestItemDetail to DimTestItemRow
        let test_items: Vec<DimTestItemRow> = sub_test_item
            .par_iter()
            .flatten()
            .into_par_iter()
            .filter_map(|elem| {
                if let Some(file_detail) = file_detail_map.get(&elem.FILE_ID?) {
                    Some(DimTestItemRow::build_test_item(elem, file_detail))
                } else {
                    log::warn!("File detail not found for FILE_ID: {:?}", elem.FILE_ID);
                    None
                }
            })
            .collect();
        log::info!("dim test_items 转换完成.");
        // Group by key and reduce (calculate time)
        let grouped_items = self.group_and_reduce(test_items);
        log::info!("dim test_items 分组完成.");
        let rows_count = grouped_items.len();

        log::info!("DIM TestItem 计算完成，共处理 {} 条记录", rows_count);
        Ok(grouped_items)
    }

    /// Group DimTestItemRow by key and reduce (calculate time)
    /// Corresponds to groupByKey and reduceGroups logic in Scala
    /// Optimized version using DashMap for concurrent grouping
    fn group_and_reduce(&self, items: Vec<DimTestItemRow>) -> Vec<DimTestItemRow> {
        log::info!("dim test item 开始分组，共 {} 条记录", items.len());
        let start_time = std::time::Instant::now();

        // Use DashMap for concurrent grouping
        let grouped: DashMap<TestItemKey, Mutex<Vec<DimTestItemRow>>> = DashMap::new();

        // Parallel grouping using rayon
        items.into_par_iter().for_each(|item| {
            let key = TestItemKey::from_dim_test_item_row(&item);
            grouped
                .entry(key)
                .or_insert_with(|| Mutex::new(Vec::new()))
                .lock()
                .unwrap()
                .push(item);
        });

        let group_duration = start_time.elapsed();
        log::info!("dim test item 分组完成，耗时: {:?}，分组数: {}", group_duration, grouped.len());

        // Parallel reduce each group (calculate time)
        let reduce_start = std::time::Instant::now();

        // Convert DashMap to Vec for parallel processing
        let groups: Vec<_> = grouped.into_iter().collect();
        let result: Vec<DimTestItemRow> = groups
            .into_par_iter()
            .map(|(_key, mutex_group)| {
                let group = mutex_group.into_inner().unwrap();
                group
                    .into_iter()
                    .reduce(|mut accumulate, right| {
                        self.calculate_test_item_ts(&mut accumulate, &right);
                        // 规约以后添加time key
                        if let Some(start_time) = accumulate.START_TIME {
                            accumulate.START_HOUR_KEY = date::get_day_hour(start_time).into();
                            accumulate.START_DAY_KEY = date::get_day(start_time).into();
                        }
                        if let Some(end_time) = accumulate.END_TIME {
                            accumulate.END_HOUR_KEY = date::get_day_hour(end_time).into();
                            accumulate.END_DAY_KEY = date::get_day(end_time).into();
                        }
                        accumulate
                    })
                    .unwrap()
            })
            .collect();

        let reduce_duration = reduce_start.elapsed();
        log::info!("dim test item 归约完成，耗时: {:?}，结果数: {}", reduce_duration, result.len());

        result
    }

    /// Calculate time for DimTestItemRow
    /// Corresponds to calculateTestItemTs method in Scala
    fn calculate_test_item_ts(&self, accumulate: &mut DimTestItemRow, right: &DimTestItemRow) {
        // Calculate time - take min start time and max end time
        if let (Some(acc_start), Some(right_start)) = (accumulate.START_TIME, right.START_TIME) {
            accumulate.START_TIME = Some(acc_start.min(right_start));
        } else if right.START_TIME.is_some() {
            accumulate.START_TIME = right.START_TIME;
        }

        if let (Some(acc_end), Some(right_end)) = (accumulate.END_TIME, right.END_TIME) {
            accumulate.END_TIME = Some(acc_end.max(right_end));
        } else if right.END_TIME.is_some() {
            accumulate.END_TIME = right.END_TIME;
        }
    }
}
