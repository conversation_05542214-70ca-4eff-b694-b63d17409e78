use chrono::{DateTime, Utc};
use rayon::prelude::*;
use std::collections::HashMap;
use std::sync::Arc;

use crate::config::DwTestItemConfig;
use common::dim::sink::test_program_test_item_handler::TestProgramTestItemHandler;
use common::dto::dim::{DimTestItemRow, DimTestProgramTestItemRow, TestProgramTestItem, TestProgramTestItemKey};
use common::dto::dwd::file_detail::FileDetail;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::model::constant::SYSTEM;
use common::utils::date;
use common::utils::decimal::Decimal38_18;

/// TestProgramTestItemService handles DIM layer TestProgramTestItem processing
/// Corresponds to TestProgramTestItemService.scala
#[derive(Debug, Clone)]
pub struct TestProgramTestItemService {
    properties: DwTestItemConfig,
}

impl TestProgramTestItemService {
    pub fn new(properties: DwTestItemConfig) -> Self {
        Self { properties }
    }

    /// Group TestProgramTestItem by key and take max version for deduplication
    /// Corresponds to groupByKey and mapGroups logic in Scala
    fn group_and_deduplicate(&self, items: Vec<TestProgramTestItem>) -> Vec<TestProgramTestItem> {
        let mut grouped: HashMap<TestProgramTestItemKey, Vec<TestProgramTestItem>> = HashMap::new();

        // Group by key
        for item in items {
            let key = item.to_key();
            grouped.entry(key).or_insert_with(Vec::new).push(item);
        }

        // Take max version from each group
        grouped
            .into_values()
            .map(|mut group| {
                group.sort_by(|a, b| b.VERSION.cmp(&a.VERSION));
                group.into_iter().next().unwrap()
            })
            .collect()
    }
    /// Write TestProgramTestItem rows to ClickHouse
    async fn write_to_clickhouse(
        &self,
        rows: Vec<DimTestProgramTestItemRow>,
        dim_db_name: &str,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if rows.is_empty() {
            log::info!("没有 TestProgramTestItem 数据需要写入");
            return Ok(());
        }

        let rows_count = rows.len();
        let ck_config = self.properties.get_ck_config(dim_db_name);
        let handler = TestProgramTestItemHandler::new(dim_db_name.to_string());

        handler.write_to_ck_generic(rows, ck_config).await?;

        log::info!("成功写入 {} 条 TestProgramTestItem 记录到 ClickHouse", rows_count);
        Ok(())
    }

    /// Calculate TestProgramTestItem from DimTestItemRow (new method for DIM layer)
    /// Corresponds to calculateTestProgramTestItem method in Scala that takes TestItem as input
    pub async fn calculate_test_program_test_item_from_test_item(
        &self,
        test_items: &Vec<DimTestItemRow>,
        dim_db_name: &str,
    ) -> Result<(Vec<DimTestProgramTestItemRow>), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("开始从 DimTestItemRow 计算 DIM TestProgramTestItem...");

        // Transform DimTestItemRow to TestProgramTestItem
        let test_program_test_items: Vec<TestProgramTestItem> = test_items
            .par_iter()
            .map(|test_item| TestProgramTestItem::from_dim_test_item(test_item))
            .collect();

        // Group by key and take max version (deduplication)
        let grouped_items = self.group_and_deduplicate(test_program_test_items);

        // Set timestamps and convert to rows
        let rows: Vec<DimTestProgramTestItemRow> = grouped_items
            .into_par_iter()
            .map(|mut item| {
                let now = Utc::now();
                item.CREATE_TIME = now;
                item.CREATE_HOUR_KEY = Arc::from(date::get_day_hour(now));
                item.CREATE_DAY_KEY = Arc::from(date::get_day(now));

                item.to_row()
            })
            .collect();

        let rows_count = rows.len();
        log::info!("从 DimTestItemRow 计算 DIM TestProgramTestItem 完成，共处理 {} 条记录", rows_count);

        Ok(rows)
    }
}
