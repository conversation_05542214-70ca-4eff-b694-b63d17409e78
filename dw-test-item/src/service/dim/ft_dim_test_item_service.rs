use crate::config::DwTestItemConfig;
use crate::service::dim::test_item_service::TestItemService;
use crate::service::dim::test_program_test_item_service::TestProgramTestItemService;
use crate::service::dim::{get_test_program_partition, write_to_ck_by_partition, write_to_clickhouse_after_tombstone};
use common::ck::ck_sink;
use common::ck::ck_sink::CkSink;
use common::dim::sink::{TestItemHandler, TestProgramTestItemHandler};
use common::dto::dim::{DimTestItem, DimTestItemRow, DimTestProgramTestItem, DimTestProgramTestItemRow};
use common::dto::dwd::file_detail::FileDetail;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::dws::model::SiteTestItemIndex;
use common::model::dw_table_enum::DwTableEnum;
use common::model::key::Wafer<PERSON>ey;
use common::parquet::RecordBatchWrapper;
use common::utils::path;
use parquet_provider::parquet_provider::{write_parquet, write_parquet_multi};
use std::collections::HashMap;
use std::error::Error;

/// FtDimTestItemService handles FT (Final Test) stage DIM layer processing
/// Corresponds to FtDimTestItemService.scala
#[derive(Debug, Clone)]
pub struct FtDimTestItemService {
    properties: DwTestItemConfig,
}

impl FtDimTestItemService {
    pub fn new(properties: DwTestItemConfig) -> Self {
        Self { properties }
    }

    /// Calculate FT DIM layer data
    /// Corresponds to calculate method in FtDimTestItemService.scala
    pub async fn calculate(
        &self,
        sub_test_item: &Vec<Vec<SubTestItemDetail>>,
        file_detail_map: &HashMap<i64, FileDetail>,
        wafer_key: WaferKey,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        log::info!("开始计算 FT DIM 层数据...");

        let dim_db_name = &self.properties.dim_db_name;

        // 1. 计算 DIM TestItem
        log::info!("开始计算 DIM TestItem...");
        let test_item_service = TestItemService::new(self.properties.clone());
        let test_items = test_item_service.calculate_test_item(sub_test_item, file_detail_map).await?;
        log::info!("DIM TestItem 计算完成，共生成 {} 条记录", test_items.len());

        let test_item_handler = TestItemHandler::new(dim_db_name.to_string(), self.properties.insert_cluster_table);
        write_to_clickhouse_after_tombstone(test_item_handler, &self.properties, &test_items, &wafer_key).await?;

        log::info!("开始计算 DIM TestProgramTestItem...");
        let test_program_test_item_service = TestProgramTestItemService::new(self.properties.clone());
        let test_program_test_item = test_program_test_item_service
            .calculate_test_program_test_item_from_test_item(&test_items, dim_db_name)
            .await?;
        log::info!("DIM TestProgramTestItem 计算完成");
        let test_program_test_item_handler = TestProgramTestItemHandler::new(dim_db_name.to_string());
        let partition = get_test_program_partition(
            wafer_key.customer.as_str(),
            wafer_key.test_area.as_str(),
            wafer_key.factory.as_str(),
        );
        write_to_ck_by_partition(
            &self.properties,
            &test_program_test_item,
            test_program_test_item_handler,
            partition.as_str(),
        )
        .await?;
        log::info!("DIM TestProgramTestItem 写入clickhouse完成");
        log::info!("FT DIM 层数据计算完成");
        Ok(())
    }

    /// Calculate FT DIM layer data with HDFS/Parquet support using RecordBatchWrapper
    ///
    /// This method extends the standard DIM calculation to support dual storage:
    /// 1. Calculates DIM data using existing business logic
    /// 2. Converts ClickHouse-optimized entities to HDFS-compatible entities
    /// 3. Writes data to both HDFS (Parquet) and ClickHouse storage systems
    ///
    /// The implementation follows the established pattern where:
    /// - Business logic remains unchanged
    /// - Data is converted between storage-specific formats
    /// - Both storage systems receive consistent data
    ///
    /// # Arguments
    /// * `sub_test_item` - Raw test item data grouped by wafer
    /// * `file_detail_map` - File metadata mapping
    /// * `wafer_key` - Wafer identification and partitioning information
    ///
    /// # Returns
    /// * `Ok(())` - Success, data written to both storage systems
    /// * `Err(Box<dyn Error + Send + Sync>)` - Storage or conversion error
    pub async fn calculate_with_parquet_support(
        &self,
        sub_test_item: &Vec<Vec<SubTestItemDetail>>,
        file_detail_map: &HashMap<i64, FileDetail>,
        wafer_key: WaferKey,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        log::info!("开始计算 FT DIM 层数据 (支持Parquet)...");

        let dim_db_name = &self.properties.dim_db_name;

        // 1. 计算 DIM TestItem
        log::info!("开始计算 DIM TestItem...");
        let test_item_service = TestItemService::new(self.properties.clone());
        let test_items = test_item_service.calculate_test_item(sub_test_item, file_detail_map).await?;
        log::info!("DIM TestItem 计算完成，共生成 {} 条记录", test_items.len());

        // Convert DimTestItemRow to DimTestItem for Parquet storage
        let hdfs_test_items: Vec<DimTestItem> = test_items
            .iter()
            .map(|row| self.convert_test_item_row_to_hdfs_entity(row))
            .collect();

        // Write to HDFS (Parquet)
        self.write_test_items_to_hdfs(&hdfs_test_items, &wafer_key).await?;

        // Write to ClickHouse
        let test_item_handler = TestItemHandler::new(dim_db_name.to_string(), self.properties.insert_cluster_table);
        write_to_clickhouse_after_tombstone(test_item_handler, &self.properties, &test_items, &wafer_key).await?;

        // 2. 计算 DIM TestProgramTestItem
        log::info!("开始计算 DIM TestProgramTestItem...");
        let test_program_test_item_service = TestProgramTestItemService::new(self.properties.clone());
        let test_program_test_item = test_program_test_item_service
            .calculate_test_program_test_item_from_test_item(&test_items, dim_db_name)
            .await?;
        log::info!("DIM TestProgramTestItem 计算完成");

        // Convert DimTestProgramTestItemRow to DimTestProgramTestItem for Parquet storage
        let hdfs_test_program_test_items: Vec<DimTestProgramTestItem> = test_program_test_item
            .iter()
            .map(|row| self.convert_test_program_test_item_row_to_hdfs_entity(row))
            .collect();

        // Write to HDFS (Parquet)
        self.write_test_program_test_items_to_hdfs(&hdfs_test_program_test_items, &wafer_key).await?;

        // Write to ClickHouse
        let test_program_test_item_handler = TestProgramTestItemHandler::new(dim_db_name.to_string());
        let partition = get_test_program_partition(
            wafer_key.customer.as_str(),
            wafer_key.test_area.as_str(),
            wafer_key.factory.as_str(),
        );
        write_to_ck_by_partition(
            &self.properties,
            &test_program_test_item,
            test_program_test_item_handler,
            partition.as_str(),
        )
        .await?;

        log::info!("DIM TestProgramTestItem 写入clickhouse完成");
        log::info!("FT DIM 层数据计算完成 (支持Parquet)");
        Ok(())
    }

    /// Convert DimTestItemRow to DimTestItem for HDFS storage
    fn convert_test_item_row_to_hdfs_entity(&self, row: &DimTestItemRow) -> DimTestItem {
        DimTestItem {
            CUSTOMER: row.CUSTOMER.clone(),
            SUB_CUSTOMER: row.SUB_CUSTOMER.clone(),
            UPLOAD_TYPE: row.UPLOAD_TYPE.clone(),
            FILE_ID: row.FILE_ID,
            FILE_NAME: row.FILE_NAME.clone(),
            FACTORY: row.FACTORY.clone(),
            FACTORY_SITE: row.FACTORY_SITE.clone(),
            FAB: row.FAB.clone(),
            FAB_SITE: row.FAB_SITE.clone(),
            TEST_AREA: row.TEST_AREA.clone(),
            TEST_STAGE: row.TEST_STAGE.clone(),
            LOT_TYPE: row.LOT_TYPE.clone(),
            DEVICE_ID: row.DEVICE_ID.clone(),
            LOT_ID: row.LOT_ID.clone(),
            SBLOT_ID: row.SBLOT_ID.clone(),
            PROCESS: row.PROCESS.clone(),
            WAFER_ID: row.WAFER_ID.clone(),
            WAFER_ID_KEY: row.WAFER_ID_KEY.clone(),
            WAFER_NO: row.WAFER_NO.clone(),
            WAFER_NO_KEY: row.WAFER_NO_KEY.clone(),
            WAFER_LOT_ID: row.WAFER_LOT_ID.clone(),
            FABWF_ID: row.FABWF_ID.clone(),
            TEST_PROGRAM: row.TEST_PROGRAM.clone(),
            TEST_PROGRAM_VERSION: row.TEST_PROGRAM_VERSION.clone(),
            TEST_TEMPERATURE: row.TEST_TEMPERATURE.clone(),
            TESTER_NAME: row.TESTER_NAME.clone(),
            TESTER_TYPE: row.TESTER_TYPE.clone(),
            OPERATOR_NAME: row.OPERATOR_NAME.clone(),
            PROBER_HANDLER_TYP: row.PROBER_HANDLER_TYP.clone(),
            PROBER_HANDLER_ID: row.PROBER_HANDLER_ID.clone(),
            PROBECARD_LOADBOARD_TYP: row.PROBECARD_LOADBOARD_TYP.clone(),
            PROBECARD_LOADBOARD_ID: row.PROBECARD_LOADBOARD_ID.clone(),
            START_TIME: row.START_TIME.map(|dt| dt.timestamp_millis()),
            END_TIME: row.END_TIME.map(|dt| dt.timestamp_millis()),
            START_HOUR_KEY: row.START_HOUR_KEY.clone(),
            START_DAY_KEY: row.START_DAY_KEY.clone(),
            END_HOUR_KEY: row.END_HOUR_KEY.clone(),
            END_DAY_KEY: row.END_DAY_KEY.clone(),
            TEST_HEAD: row.TEST_HEAD,
            SITE: row.SITE,
            SITE_KEY: row.SITE_KEY.clone(),
            HBIN_NUM: row.HBIN_NUM,
            HBIN_NUM_KEY: row.HBIN_NUM_KEY.clone(),
            SBIN_NUM: row.SBIN_NUM,
            SBIN_NUM_KEY: row.SBIN_NUM_KEY.clone(),
            SBIN_PF: row.SBIN_PF.clone(),
            SBIN_NAM: row.SBIN_NAM.clone(),
            HBIN_PF: row.HBIN_PF.clone(),
            HBIN_NAM: row.HBIN_NAM.clone(),
            HBIN: row.HBIN.clone(),
            SBIN: row.SBIN.clone(),
            TEST_NUM: row.TEST_NUM,
            TEST_TXT: row.TEST_TXT.clone(),
            TEST_ITEM: row.TEST_ITEM.clone(),
            TESTITEM_TYPE: row.TESTITEM_TYPE.clone(),
            ORIGIN_HI_LIMIT: row.ORIGIN_HI_LIMIT.map(|d| d.to_string().parse::<f64>().unwrap_or(0.0)),
            ORIGIN_LO_LIMIT: row.ORIGIN_LO_LIMIT.map(|d| d.to_string().parse::<f64>().unwrap_or(0.0)),
            ORIGIN_UNITS: row.ORIGIN_UNITS.clone(),
            LO_LIMIT: row.LO_LIMIT.map(|d| d.to_string().parse::<f64>().unwrap_or(0.0)),
            HI_LIMIT: row.HI_LIMIT.map(|d| d.to_string().parse::<f64>().unwrap_or(0.0)),
            UNITS: row.UNITS.clone(),
            CONDITION_SET: row.CONDITION_SET.clone(),
            CREATE_HOUR_KEY: row.CREATE_HOUR_KEY.clone(),
            CREATE_DAY_KEY: row.CREATE_DAY_KEY.clone(),
            CREATE_TIME: row.CREATE_TIME.timestamp_millis(),
            CREATE_USER: row.CREATE_USER.clone(),
            UPLOAD_TIME: row.UPLOAD_TIME.timestamp_millis(),
            VERSION: row.VERSION,
            IS_DELETE: row.IS_DELETE,
        }
    }

    /// Convert DimTestProgramTestItemRow to DimTestProgramTestItem for HDFS storage
    fn convert_test_program_test_item_row_to_hdfs_entity(&self, row: &DimTestProgramTestItemRow) -> DimTestProgramTestItem {
        DimTestProgramTestItem {
            CUSTOMER: row.CUSTOMER.clone(),
            SUB_CUSTOMER: row.SUB_CUSTOMER.clone(),
            UPLOAD_TYPE: row.UPLOAD_TYPE.clone(),
            FILE_ID: row.FILE_ID,
            FACTORY: row.FACTORY.clone(),
            FACTORY_SITE: row.FACTORY_SITE.clone(),
            FAB: row.FAB.clone(),
            FAB_SITE: row.FAB_SITE.clone(),
            TEST_AREA: row.TEST_AREA.clone(),
            TEST_STAGE: row.TEST_STAGE.clone(),
            DEVICE_ID: row.DEVICE_ID.clone(),
            TEST_PROGRAM: row.TEST_PROGRAM.clone(),
            TEST_PROGRAM_VERSION: row.TEST_PROGRAM_VERSION.clone(),
            TEST_TEMPERATURE: row.TEST_TEMPERATURE.clone(),
            TEST_NUM: row.TEST_NUM,
            TEST_NUM_KEY: row.TEST_NUM_KEY.clone(),
            TEST_TXT: row.TEST_TXT.clone(),
            TEST_ITEM: row.TEST_ITEM.clone(),
            TESTITEM_TYPE: row.TESTITEM_TYPE.clone(),
            ORIGIN_HI_LIMIT: row.ORIGIN_HI_LIMIT.map(|d| d.to_string().parse::<f64>().unwrap_or(0.0)),
            ORIGIN_HI_LIMIT_KEY: row.ORIGIN_HI_LIMIT_KEY.clone(),
            ORIGIN_LO_LIMIT: row.ORIGIN_LO_LIMIT.map(|d| d.to_string().parse::<f64>().unwrap_or(0.0)),
            ORIGIN_LO_LIMIT_KEY: row.ORIGIN_LO_LIMIT_KEY.clone(),
            ORIGIN_UNITS: row.ORIGIN_UNITS.clone(),
            LO_LIMIT: row.LO_LIMIT.map(|d| d.to_string().parse::<f64>().unwrap_or(0.0)),
            LO_LIMIT_KEY: row.LO_LIMIT_KEY.clone(),
            HI_LIMIT: row.HI_LIMIT.map(|d| d.to_string().parse::<f64>().unwrap_or(0.0)),
            HI_LIMIT_KEY: row.HI_LIMIT_KEY.clone(),
            UNITS: row.UNITS.clone(),
            CONDITION_SET: row.CONDITION_SET.clone(),
            CONDITION_SET_STR: row.CONDITION_SET_STR.clone(),
            CREATE_HOUR_KEY: row.CREATE_HOUR_KEY.clone(),
            CREATE_DAY_KEY: row.CREATE_DAY_KEY.clone(),
            CREATE_TIME: row.CREATE_TIME.timestamp_millis(),
            CREATE_USER: row.CREATE_USER.clone(),
            UPLOAD_TIME: row.UPLOAD_TIME.timestamp_millis(),
            VERSION: row.VERSION,
            IS_DELETE: row.IS_DELETE,
        }
    }

    /// Write DimTestItem data to HDFS using Parquet format
    async fn write_test_items_to_hdfs(
        &self,
        data: &[DimTestItem],
        wafer_key: &WaferKey,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        if data.is_empty() {
            log::warn!("No DimTestItem data to write to HDFS");
            return Ok(());
        }

        log::info!("开始写入DimTestItem到HDFS，数据量: {}", data.len());

        // Create RecordBatch using RecordBatchWrapper
        let record_batch = DimTestItem::to_record_batch(data)
            .map_err(|e| format!("Failed to create RecordBatch for DimTestItem: {}", e))?;

        // Generate HDFS path using existing path utility
        let base_path = "/user/glory/data/onedata/dataware/dim/result/{TABLE}/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/WAFER_NO={WAFER_NO}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}";
        let hdfs_path = path::get_wafer_path(
            base_path,
            "dim_test_item",
            &wafer_key.test_area,
            &wafer_key.customer,
            &wafer_key.factory,
            &wafer_key.lot_id,
            &wafer_key.wafer_no,
            &wafer_key.device_id,
            &wafer_key.test_stage,
            &wafer_key.lot_type,
        );

        // Write to Parquet using RecordBatchWrapper
        let data_vec = data.to_vec();
        write_parquet(&hdfs_path, "dim_test_item.parquet", &data_vec, None).await?;

        log::info!("DimTestItem写入HDFS完成: {}", hdfs_path);
        Ok(())
    }

    /// Write DimTestProgramTestItem data to HDFS using Parquet format
    async fn write_test_program_test_items_to_hdfs(
        &self,
        data: &[DimTestProgramTestItem],
        wafer_key: &WaferKey,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        if data.is_empty() {
            log::warn!("No DimTestProgramTestItem data to write to HDFS");
            return Ok(());
        }

        log::info!("开始写入DimTestProgramTestItem到HDFS，数据量: {}", data.len());

        // Create RecordBatch using RecordBatchWrapper
        let record_batch = DimTestProgramTestItem::to_record_batch(data)
            .map_err(|e| format!("Failed to create RecordBatch for DimTestProgramTestItem: {}", e))?;

        // Generate HDFS path using existing path utility
        let base_path = "/user/glory/data/onedata/dataware/dim/result/{TABLE}/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/WAFER_NO={WAFER_NO}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}";
        let hdfs_path = path::get_wafer_path(
            base_path,
            "dim_test_program_test_item",
            &wafer_key.test_area,
            &wafer_key.customer,
            &wafer_key.factory,
            &wafer_key.lot_id,
            &wafer_key.wafer_no,
            &wafer_key.device_id,
            &wafer_key.test_stage,
            &wafer_key.lot_type,
        );

        // Write to Parquet using RecordBatchWrapper
        let data_vec = data.to_vec();
        write_parquet(&hdfs_path, "dim_test_program_test_item.parquet", &data_vec, None).await?;

        log::info!("DimTestProgramTestItem写入HDFS完成: {}", hdfs_path);
        Ok(())
    }
}
