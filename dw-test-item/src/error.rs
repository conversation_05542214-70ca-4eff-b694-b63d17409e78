use thiserror::Error;

#[derive(Debug, Error)]
pub enum DatawareError {
    #[error("Tombstone failed: {0}")]
    TombstoneFailed(String),

    #[error("Dwd Calculate failed: {0}")]
    DwdCalculateFailed(String),

    #[error("Dws Calculate failed: {0}")]
    DwsCalculateFailed(String),

    #[error("Dim Calculate failed: {0}")]
    DimCalculateFailed(String),

    #[error("Ads Calculate failed: {0}")]
    AdsCalculateFailed(String),
}
