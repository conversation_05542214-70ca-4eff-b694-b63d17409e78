CREATE TABLE IF NOT EXISTS ads.ads_yms_stage_test_item_program_bin_local ON cluster cluster_3shards_1replicas
(
    `DATA_SOURCE`                                       String COMMENT '数据源类型,MES测报数据:MES,测试RAW数据:TEST RAW DATA & CP MAP & INKLESS MAP' CODEC(ZSTD(1)),
    `UPLOAD_TYPE`                                       String COMMENT '文件类型,应用端上传:MANUAL,量产FTP拉取:AUTO' CODEC(ZSTD(1)),
    `CUSTOMER`                                          String COMMENT '客户名' CODEC(ZSTD(1)),
    `SUB_CUSTOMER`                                      String COMMENT '子客户名' CODEC(ZSTD(1)),
    `FAB`                                               String COMMENT '晶圆的生产厂' CODEC(ZSTD(1)),
    `FAB_SITE`                                          String COMMENT '晶圆的生产厂子厂' CODEC(ZSTD(1)),
    `FACTORY`                                           String COMMENT '测试工厂' CODEC(ZSTD(1)),
    `FACTORY_SITE`                                      String COMMENT '测试工厂子厂' CODEC(ZSTD(1)),
    `TEST_AREA`                                         String COMMENT '测试阶段大类型,CP/FT/SLT/WAT/CP(MAP)等' CODEC(ZSTD(1)),
    `TEST_STAGE`                                        String COMMENT '具体测试阶段,CPX/FTX/SLTX/WATX' CODEC(ZSTD(1)),
    `DEVICE_ID`                                         String COMMENT '代替PART_TYP' CODEC(ZSTD(1)),
    `LOT_TYPE`                                          String COMMENT 'LOT类型, ENGINEERING/PRODUCTION等' CODEC(ZSTD(1)),
    `LOT_ID`                                            String COMMENT '测试批次ID' CODEC(ZSTD(1)),
    `SBLOT_ID`                                          String COMMENT '测试子批次ID' CODEC(ZSTD(1)),
    `WAFER_ID`                                          String COMMENT '晶圆ID' CODEC(ZSTD(1)),
    `WAFER_NO`                                          String COMMENT '晶圆编号' CODEC(ZSTD(1)),
    `WAFER_LOT_ID`                                      String COMMENT '只记录FT对应CP的LOT_ID;其他阶段为空字符串' CODEC(ZSTD(1)),
    `PRODUCT`                                           String COMMENT 'PRODUCT' CODEC(ZSTD(1)),
    `PRODUCT_TYPE`                                      String COMMENT 'PRODUCT_TYPE' CODEC(ZSTD(1)),
    `PRODUCT_FAMILY`                                    String COMMENT 'PRODUCT_FAMILY' CODEC(ZSTD(1)),
    `TEST_PROGRAM`                                      String COMMENT '测试的程序名称' CODEC(ZSTD(1)),
    `TEST_PROGRAM_VERSION`                              String COMMENT '测试程序版本' CODEC(ZSTD(1)),
    `TEST_NUM`                                Nullable(UInt32) COMMENT '测试项编号' CODEC(Delta, ZSTD),
    `TEST_TXT`                                          String COMMENT '测试项名称' CODEC(ZSTD(1)),
    `TEST_ITEM`                                         String COMMENT '测试项,TEST_NUM:TEST_TXT' CODEC(ZSTD(1)),
    `HBIN`                                              String COMMENT 'HBIN_NUM-SBIN_NAM' CODEC(ZSTD(1)),
    `SBIN`                                              String COMMENT 'SBIN_NUM-SBIN_NAM' CODEC(ZSTD(1)),
    `HBIN_NUM`                                Nullable(UInt32) COMMENT 'HBIN NUMBER' CODEC(Delta, ZSTD),
    `HBIN_NAM`                                          String COMMENT 'HBIN NAME' CODEC(ZSTD(1)),
    `HBIN_PF`                                           String COMMENT 'HBIN PASS&FAIL' CODEC(ZSTD(1)),
    `SBIN_NUM`                                Nullable(UInt32) COMMENT 'SBIN NUMBER' CODEC(Delta, ZSTD),
    `SBIN_NAM`                                          String COMMENT 'SBIN NAME' CODEC(ZSTD(1)),
    `SBIN_PF`                                           String COMMENT 'SBIN PASS&FAIL' CODEC(ZSTD(1)),
    `IS_PASS_ONLY`                                       UInt8 COMMENT '是否仅计算PASS_DIE' CODEC(Delta, ZSTD),
    `IS_FINAL`                                           UInt8 COMMENT '是否为最终测试结果:0/1' CODEC(Delta, ZSTD),
    `UNITS_LIST`                                        String COMMENT '测试项单位去重列表' CODEC(ZSTD(1)),
    `ORIGIN_UNITS_LIST`                                 String COMMENT 'STDF原始UNITS去重列表' CODEC(ZSTD(1)),
    `LO_LIMIT_LIST`                                     String COMMENT '测试下限去重列表' CODEC(ZSTD(1)),
    `HI_LIMIT_LIST`                                     String COMMENT '测试上限去重列表' CODEC(ZSTD(1)),
    `ORIGIN_LO_LIMIT_LIST`                              String COMMENT '原始下限去重列表' CODEC(ZSTD(1)),
    `ORIGIN_HI_LIMIT_LIST`                              String COMMENT '原始上限去重列表' CODEC(ZSTD(1)),
    `PROCESS_LIST`                                      String COMMENT 'PROCESS去重列表' CODEC(ZSTD(1)),
    `TESTITEM_TYPE_LIST`                                String COMMENT 'TESTITEM_TYPE去重列表' CODEC(ZSTD(1)),
    `TEST_TEMPERATURE_LIST`                             String COMMENT '测试温度去重列表' CODEC(ZSTD(1)),
    `TESTER_NAME_LIST`                                  String COMMENT 'TESTER名称,STDF的NODE_NAM去重列表' CODEC(ZSTD(1)),
    `TESTER_TYPE_LIST`                                  String COMMENT 'STDF的TSTR_TYP去重列表' CODEC(ZSTD(1)),
    `PROBER_HANDLER_TYP_LIST`                           String COMMENT 'FT:HANDLER,CP:PROBER,取自STDF文件的HAND_TYP去重列表' CODEC(ZSTD(1)),
    `PROBER_HANDLER_ID_LIST`                            String COMMENT '测试FT:HANDLER,CP:PROBER,取自STDF文件的HAND ID去重列表' CODEC(ZSTD(1)),
    `PROBECARD_LOADBOARD_TYP_LIST`                      String COMMENT 'CP:PROBECARD,取CARD_TYP FT:LOADBOARD,取LOAD_TYPE去重列表' CODEC(ZSTD(1)),
    `PROBECARD_LOADBOARD_ID_LIST`                       String COMMENT '最终测试CP:CARD ID,FT:LOAD ID去重列表' CODEC(ZSTD(1)),
    `RETEST_BIN_NUM_LIST`                               String COMMENT '复测BIN去重列表' CODEC(ZSTD(1)),
    `INPUT_CNT`                                          Int32 COMMENT '测试数量' CODEC(Delta, ZSTD),
    `PASS_CNT`                                           Int32 COMMENT 'PASS数量' CODEC(Delta, ZSTD),
    `FAIL_CNT`                                           Int32 COMMENT 'FAIL数量' CODEC(Delta, ZSTD),
    `PASSBIN_FAILINGITEM_CNT`                            Int32 COMMENT '忽略ITEM的PASS数量' CODEC(Delta, ZSTD),
    `EXE_INPUT_CNT`                                      Int32 COMMENT '测试结果TEST_VALUE总数' CODEC(Delta, ZSTD),
    `EXE_PASS_CNT`                                       Int32 COMMENT '测试PASSTEST_VALUE总数' CODEC(Delta, ZSTD),
    `EXE_FAIL_CNT`                                       Int32 COMMENT '测试FAILTEST_VALUE总数' CODEC(Delta, ZSTD),
    `MEDIAN`                          Nullable(Decimal(38,18)) COMMENT '中位数' CODEC(ZSTD(1)),
    `MEAN`                            Nullable(Decimal(38,18)) COMMENT '算术平均数' CODEC(ZSTD(1)),
    `MAX`                             Nullable(Decimal(38,18)) COMMENT '最大值' CODEC(ZSTD(1)),
    `MIN`                             Nullable(Decimal(38,18)) COMMENT '最小值' CODEC(ZSTD(1)),
    `MAX_WO_OUTLIERS`                 Nullable(Decimal(38,18)) COMMENT '去掉OUTLIERS之后的最大值' CODEC(ZSTD(1)),
    `MIN_WO_OUTLIERS`                 Nullable(Decimal(38,18)) COMMENT '去掉OUTLIERS之后的最小值' CODEC(ZSTD(1)),
    `SUM_SQ`                          Nullable(Decimal(38,18)) COMMENT '平方和' CODEC(ZSTD(1)),
    `SUM_VALUE`                       Nullable(Decimal(38,18)) COMMENT '算术和' CODEC(ZSTD(1)),
    `STDEV_P`                         Nullable(Decimal(38,18)) COMMENT '总体标准差' CODEC(ZSTD(1)),
    `STDEV_S`                         Nullable(Decimal(38,18)) COMMENT '样本标准差' CODEC(ZSTD(1)),
    `RANGE`                           Nullable(Decimal(38,18)) COMMENT '最大最小差' CODEC(ZSTD(1)),
    `IQR`                             Nullable(Decimal(38,18)) COMMENT 'Q3-Q1分位差' CODEC(ZSTD(1)),
    `Q1`                              Nullable(Decimal(38,18)) COMMENT '1/4分位数' CODEC(ZSTD(1)),
    `Q3`                              Nullable(Decimal(38,18)) COMMENT '3/4分位数' CODEC(ZSTD(1)),
    `LOWER`                           Nullable(Decimal(38,18)) COMMENT '下界Q1-1.5*(Q3-Q1)' CODEC(ZSTD(1)),
    `UPPER`                           Nullable(Decimal(38,18)) COMMENT '上界Q3+1.5*(Q3-Q1)' CODEC(ZSTD(1)),
    `OUTLIER_CNT`                                       UInt32 COMMENT '离群点数量' CODEC(Delta, ZSTD),
    `P1`                              Nullable(Decimal(38,18)) COMMENT '1%分位点' CODEC(ZSTD(1)),
    `P5`                              Nullable(Decimal(38,18)) COMMENT '5%分位点' CODEC(ZSTD(1)),
    `P10`                             Nullable(Decimal(38,18)) COMMENT '10%分位点' CODEC(ZSTD(1)),
    `P90`                             Nullable(Decimal(38,18)) COMMENT '90%分位点' CODEC(ZSTD(1)),
    `P95`                             Nullable(Decimal(38,18)) COMMENT '95%分位点' CODEC(ZSTD(1)),
    `P99`                             Nullable(Decimal(38,18)) COMMENT '99%分位点' CODEC(ZSTD(1)),
    `GROUP_DETAIL`                          Map(String,UInt32) COMMENT '分组与分组内数量' CODEC(ZSTD(1)),
    `PP`                              Nullable(Decimal(38,18)) COMMENT '过程能力指数PP :(USL-LSL)/6*STDEV_S ' CODEC(ZSTD(1)),
    `PPU`                             Nullable(Decimal(38,18)) COMMENT '过程能力指数PPU:(USL-AVG)/3*STDEV_S' CODEC(ZSTD(1)),
    `PPL`                             Nullable(Decimal(38,18)) COMMENT '过程能力指数PPL:(AVG-LSL)/3*STDEV_S' CODEC(ZSTD(1)),
    `PPK`                             Nullable(Decimal(38,18)) COMMENT 'MIN(PPU,PPL)' CODEC(ZSTD(1)),
    `CP`                              Nullable(Decimal(38,18)) COMMENT '过程能力指数CP,双边规格' CODEC(ZSTD(1)),
    `CPU`                             Nullable(Decimal(38,18)) COMMENT '过程能力指数CPU(单边规格上限)' CODEC(ZSTD(1)),
    `CPL`                             Nullable(Decimal(38,18)) COMMENT '过程能力指数CPL(单边规格下限).S' CODEC(ZSTD(1)),
    `CPK`                             Nullable(Decimal(38,18)) COMMENT 'MIN(CPU,CPL)' CODEC(ZSTD(1)),
    `CA`                              Nullable(Decimal(38,18)) COMMENT '制程准确度' CODEC(ZSTD(1)),
    `SKEWNESS`                        Nullable(Decimal(38,18)) COMMENT '偏度' CODEC(ZSTD(1)),
    `KURTOSIS`                        Nullable(Decimal(38,18)) COMMENT '峰度' CODEC(ZSTD(1)),
    `NORMALIZATION_MEDIAN`            Nullable(Decimal(38,18)) COMMENT '归一化中位数' CODEC(ZSTD(1)),
    `NORMALIZATION_MEAN`              Nullable(Decimal(38,18)) COMMENT '归一化算术平均数' CODEC(ZSTD(1)),
    `NORMALIZATION_MAX`               Nullable(Decimal(38,18)) COMMENT '归一化最大值' CODEC(ZSTD(1)),
    `NORMALIZATION_MIN`               Nullable(Decimal(38,18)) COMMENT '归一化最小值' CODEC(ZSTD(1)),
    `NORMALIZATION_MAX_WO_OUTLIERS`   Nullable(Decimal(38,18)) COMMENT '去掉OUTLIERS之后的归一化最大值' CODEC(ZSTD(1)),
    `NORMALIZATION_MIN_WO_OUTLIERS`   Nullable(Decimal(38,18)) COMMENT '去掉OUTLIERS之后的归一化最小值' CODEC(ZSTD(1)),
    `NORMALIZATION_IQR`               Nullable(Decimal(38,18)) COMMENT '归一化Q3-Q1' CODEC(ZSTD(1)),
    `NORMALIZATION_Q1`                Nullable(Decimal(38,18)) COMMENT '归一化1/4分位数' CODEC(ZSTD(1)),
    `NORMALIZATION_Q3`                Nullable(Decimal(38,18)) COMMENT '归一化3/4分位数' CODEC(ZSTD(1)),
    `NORMALIZATION_LOWER`             Nullable(Decimal(38,18)) COMMENT '归一化Q1-1.5*(Q3-Q1)' CODEC(ZSTD(1)),
    `NORMALIZATION_UPPER`             Nullable(Decimal(38,18)) COMMENT '归一化Q3+1.5*(Q3-Q1)' CODEC(ZSTD(1)),
    `START_TIME`                            Nullable(DateTime) COMMENT 'WAFER&SBLOT的测试开始时间' CODEC(Delta, ZSTD),
    `START_HOUR_KEY`                                    String COMMENT '开始时间(小时)' CODEC(ZSTD(1)),
    `START_DAY_KEY`                                     String COMMENT '开始时间(天)' CODEC(ZSTD(1)),
    `END_TIME`                              Nullable(DateTime) COMMENT 'WAFER&SBLOT的测试结束时间' CODEC(Delta, ZSTD),
    `END_HOUR_KEY`                                      String COMMENT '结束时间(小时)' CODEC(ZSTD(1)),
    `END_DAY_KEY`                                       String COMMENT '结束时间(天)' CODEC(ZSTD(1)),
    `CREATE_TIME`                                     DateTime COMMENT '创建时间,系统字段' CODEC(Delta, ZSTD),
    `CREATE_HOUR_KEY`                                   String COMMENT '创建时间(小时)' CODEC(ZSTD(1)),
    `CREATE_DAY_KEY`                                    String COMMENT '创建时间(天)' CODEC(ZSTD(1)),
    `CREATE_USER`                                       String COMMENT '创建用户，系统字段' CODEC(ZSTD(1)),
    `UPLOAD_TIME`                                     DateTime COMMENT '数据上传时间,系统字段' CODEC(Delta, ZSTD),
    `VERSION`                                  Int64 DEFAULT 0 COMMENT '数据版本(用于YMS数据更新)' CODEC(Delta, ZSTD),
    `IS_DELETE`                                UInt8 DEFAULT 0 COMMENT '是否删除' CODEC(Delta, ZSTD)
) ENGINE = ReplicatedMergeTree
      PARTITION BY (DATA_SOURCE, CUSTOMER , UPLOAD_TYPE , TEST_AREA , FACTORY , SUB_CUSTOMER , DEVICE_ID)
      ORDER BY (TEST_ITEM , TEST_STAGE , LOT_ID)
      SETTINGS index_granularity = 8192;