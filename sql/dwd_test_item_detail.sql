create table if not exists dwd.dwd_test_item_detail_local on cluster cluster_3shards_1replicas
(
    `ID`                      String COMMENT 'UUID' CODEC(ZSTD(1)),
    `CUSTOMER`                String COMMENT '客户名' CODEC(ZSTD(1)),
    `SUB_CUSTOMER`            String COMMENT '子客户名' CODEC(ZSTD(1)),
    `UPLOAD_TYPE`             String COMMENT '文件类型, 应用端上传过来的设置为: MANUAL, 量产ftp拉过来的设置为: AUTO' CODEC(ZSTD(1)),
    `FILE_ID`                 UInt32 COMMENT '数据中心文件ID' CODEC(Delta, ZSTD),
    `FILE_NAME`               String COMMENT '文件名，数据中心文件名' CODEC(ZSTD(1)),
    `FILE_TYPE`               String COMMENT '文件类型， TXT/CSV/SNP/STDF等' CODEC(ZSTD(1)),
    `DEVICE_ID`               String COMMENT '代替PART_TYP' CODEC(ZSTD(1)),
    `FACTORY`                 String COMMENT '测试工厂' CODEC(ZSTD(1)),
    `FACTORY_SITE`            String COMMENT '测试工厂子厂' CODEC(ZSTD(1)),
    `FAB`                     String COMMENT '晶圆的生产厂' CODEC(ZSTD(1)),
    `FAB_SITE`                String COMMENT '晶圆的生产厂子厂' CODEC(ZSTD(1)),
    `LOT_TYPE`                String COMMENT 'Lot类型，ENGINEERING/PRODUCTION等' CODEC(ZSTD(1)),
    `LOT_ID`                  String COMMENT 'Lot Id ' CODEC(ZSTD(1)),
    `PROCESS`                 String CODEC(ZSTD(1)),
    `SBLOT_ID`                String COMMENT 'SubLot Id' CODEC(ZSTD(1)),
    `WAFER_LOT_ID`            String COMMENT '晶圆母批次号,CP为LOT_ID,FT为对应CP的LOT ID' CODEC(ZSTD(1)),
    `TEST_AREA`               String COMMENT 'CP/FT/WAT/SLT/EQC/KGU/LAB/BURNIN等 表示测试阶段大类型' CODEC(ZSTD(1)),
    `TEST_STAGE`              String COMMENT 'CP1/CP2/FT1/FT2/WAT1/SLT1/EQC1/KGU1/CHAR/VALIDTION/VERIFICATION 表示具体测试阶段' CODEC(ZSTD(1)),
    `OFFLINE_RETEST` Nullable(UInt8) COMMENT 'OFFLINE复测序列 0， 1，2' CODEC(Delta, ZSTD),
    `ONLINE_RETEST` Nullable(UInt8) COMMENT 'ONLINE复测序列 0， 1，2' CODEC(Delta, ZSTD),
    `INTERRUPT` Nullable(UInt8) COMMENT '中断测试， 0， 1，2' CODEC(Delta, ZSTD),
    `DUP_RETEST` Nullable(UInt8) COMMENT '重测， 0， 1，2' CODEC(Delta, ZSTD),
    `BATCH_NUM` Nullable(UInt8) COMMENT '分批批次， 0， 1，2' CODEC(Delta, ZSTD),
    `OFFLINE_RETEST_IGNORE_TP` Nullable(UInt8) COMMENT '忽略测试程序，OFFLINE复测序列 0， 1，2' CODEC(Delta, ZSTD),
    `INTERRUPT_IGNORE_TP` Nullable(UInt8) COMMENT '忽略测试程序，中断测试， 0， 1，2' CODEC(Delta, ZSTD),
    `DUP_RETEST_IGNORE_TP` Nullable(UInt8) COMMENT '忽略测试程序，重测， 0， 1，2' CODEC(Delta, ZSTD),
    `BATCH_NUM_IGNORE_TP` Nullable(UInt8) COMMENT '忽略测试程序，分批批次， 0， 1，2' CODEC(Delta, ZSTD),
    `MAX_OFFLINE_RETEST` Nullable(UInt8) COMMENT '最大OFFLINE测试序列' CODEC(Delta, ZSTD),
    `MAX_ONLINE_RETEST` Nullable(UInt8) default ONLINE_RETEST COMMENT '每个文件每个ECID最大的ONLINE_RETEST' CODEC(Delta, ZSTD),
    `IS_DIE_FIRST_TEST` Nullable(UInt8) COMMENT '是否DIE第一次测试FLAG: 0/1，从die detail补' CODEC(Delta, ZSTD),
    `IS_DIE_FINAL_TEST` Nullable(UInt8) COMMENT '是否DIE最后一次测试FLAG: 0/1，从die detail补' CODEC(Delta, ZSTD),
    `IS_FIRST_TEST` Nullable(UInt8) COMMENT '是否第一次测试FLAG: 0/1' CODEC(Delta, ZSTD),
    `IS_FINAL_TEST` Nullable(UInt8) COMMENT '是否最后一次测试FLAG: 0/1，基于OFFLINE_RETEST，ONLINE_RETEST，INTERRUPT，DUP_RETEST计算' CODEC(Delta, ZSTD),
    `IS_FIRST_TEST_IGNORE_TP` Nullable(UInt8) default 1 comment '忽略Test Program,是否第一次测试FLAG: 0/1' CODEC(Delta, ZSTD),
    `IS_FINAL_TEST_IGNORE_TP` Nullable(UInt8) default 1 comment '忽略Test Program,是否最后一次测试FLAG: 0/1' CODEC(Delta, ZSTD),
    `IS_DUP_FIRST_TEST` Nullable(UInt8) COMMENT '是否第一次重新测试FLAG: 0/1' CODEC(Delta, ZSTD),
    `IS_DUP_FINAL_TEST` Nullable(UInt8) COMMENT '是否最后一次重新测试FLAG: 0/1，基于OFFLINE_RETEST，ONLINE_RETEST，INTERRUPT，DUP_RETEST计算' CODEC(Delta, ZSTD),
    `IS_DUP_FIRST_TEST_IGNORE_TP` Nullable(UInt8) default IS_DUP_FIRST_TEST comment '忽略Test Program,是否第一次重新测试FLAG: 0/1' CODEC(Delta, ZSTD),
    `IS_DUP_FINAL_TEST_IGNORE_TP` Nullable(UInt8) default IS_DUP_FINAL_TEST comment '忽略Test Program,是否最后一次重新测试FLAG: 0/1，基于OFFLINE_RETEST，ONLINE_RETEST，INTERRUPT，DUP_RETEST计算' CODEC(Delta, ZSTD),
    `TEST_SUITE`              String COMMENT '测试Suite或者在研发测试阶段的Test Case测试用例' CODEC(ZSTD(1)),
    `CONDITION_SET` Map(String, String) COMMENT '测试条件集合，用于测试条件动态扩展，Map动态类型{Key: Value}' CODEC(ZSTD(1)),
    `TEST_NUM` Nullable(UInt32) CODEC(Delta, ZSTD),
    `TEST_TXT`                String CODEC(ZSTD(1)),
    `TEST_ITEM`               String COMMENT 'TEST_NUM:TEST_TXT，业务上用该字段' CODEC(ZSTD(1)),
    `IS_DIE_FIRST_TEST_ITEM` Nullable(UInt8) COMMENT '是否是die的第一个测项(PTR/MPR)FLAG: 0/1' CODEC(Delta, ZSTD),
    `TESTITEM_TYPE`           String COMMENT 'P/F/M' CODEC(ZSTD(1)),
    `TEST_FLG`                String CODEC(ZSTD(1)),
    `PARM_FLG`                String CODEC(ZSTD(1)),
    `TEST_STATE`              String COMMENT 'VALID/INVALID， 根据Test_FLG和PARM_FLG字段计算' CODEC(ZSTD(1)),
    `TEST_VALUE` Nullable(Decimal(38, 18)) COMMENT 'PTR/MPR测试Result数值，标准化之后，Decimal类型，精度18？？' CODEC(ZSTD(1)),
    `UNITS`                   String COMMENT '测试项单位' CODEC(ZSTD(1)),
    `TEST_RESULT` Nullable(UInt8) COMMENT '最终测试结果 0/1，0表示Fail，1表示Pass' CODEC(Delta, ZSTD),
    `ORIGIN_TEST_VALUE` Nullable(Decimal(38, 18)) COMMENT 'STDF原始PTR/MPR测试Result，Decimal类型，精度18？？' CODEC(ZSTD(1)),
    `ORIGIN_UNITS`            String COMMENT 'STDF原始Units' CODEC(ZSTD(1)),
    `TEST_ORDER` Nullable(UInt32) COMMENT '测试顺序' CODEC(Delta, ZSTD),
    `ALARM_ID`                String CODEC(ZSTD(1)),
    `OPT_FLG`                 String CODEC(ZSTD(1)),
    `RES_SCAL` Nullable(Int32) COMMENT 'Test Value的Scale，来自于STDF  RES_SCAL' CODEC(Delta, ZSTD),
    `NUM_TEST` Nullable(Int32) COMMENT '芯片TESTITEM执行个数' CODEC(Delta, ZSTD),
    `TEST_PROGRAM`            String COMMENT '测试程序名称，可能来自于STDF，也可能来自于其他地方' CODEC(ZSTD(1)),
    `TEST_TEMPERATURE`        String COMMENT '测试温度' CODEC(ZSTD(1)),
    `TEST_PROGRAM_VERSION`    String COMMENT '测试程序版本，可能来自于STDF，也可能来自于其他地方' CODEC(ZSTD(1)),
    `LLM_SCAL` Nullable(Int32) CODEC(Delta, ZSTD),
    `HLM_SCAL` Nullable(Int32) CODEC(Delta, ZSTD),
    `LO_LIMIT` Nullable(Decimal(38, 18)) COMMENT '保留18位小数' CODEC(ZSTD(1)),
    `HI_LIMIT` Nullable(Decimal(38, 18)) COMMENT '保留18位小数' CODEC(ZSTD(1)),
    `ORIGIN_HI_LIMIT` Nullable(Decimal(38, 18)) COMMENT '保留18位小数' CODEC(ZSTD(1)),
    `ORIGIN_LO_LIMIT` Nullable(Decimal(38, 18)) COMMENT '保留18位小数' CODEC(ZSTD(1)),
    `C_RESFMT`                String CODEC(ZSTD(1)),
    `C_LLMFMT`                String CODEC(ZSTD(1)),
    `C_HLMFMT`                String CODEC(ZSTD(1)),
    `LO_SPEC` Nullable(Decimal(38, 18)) COMMENT '保留18位小数' CODEC(ZSTD(1)),
    `HI_SPEC` Nullable(Decimal(38, 18)) COMMENT '保留18位小数' CODEC(ZSTD(1)),
    `SPEC_NAM`                String CODEC(ZSTD(1)),
    `SPEC_VER`                String CODEC(ZSTD(1)),
    `HBIN_NUM` Nullable(UInt32) CODEC(Delta, ZSTD),
    `SBIN_NUM` Nullable(UInt32) CODEC(Delta, ZSTD),
    `SBIN_PF`                 String CODEC(ZSTD(1)),
    `SBIN_NAM`                String CODEC(ZSTD(1)),
    `HBIN_PF`                 String CODEC(ZSTD(1)),
    `HBIN_NAM`                String CODEC(ZSTD(1)),
    `HBIN`                    String COMMENT 'HBIN_NUM-HBIN_NAM' CODEC(ZSTD(1)),
    `SBIN`                    String COMMENT 'SBIN_NUM-SBIN_NAM' CODEC(ZSTD(1)),
    `TEST_HEAD` Nullable(UInt32) CODEC(Delta, ZSTD),
    `TESTER_NAME`             String COMMENT 'TESTER名称，STDF的NODE_NAM' CODEC(ZSTD(1)),
    `TESTER_TYPE`             String COMMENT 'STDF的TSTR_TYP' CODEC(ZSTD(1)),
    `OPERATOR_NAME`           String COMMENT 'OPERATOR 名字' CODEC(ZSTD(1)),
    `PROBER_HANDLER_TYP`      String COMMENT 'FT:  HANDLER, CP: PROBER, 取自STDF文件的HAND_TYP' CODEC(ZSTD(1)),
    `PROBER_HANDLER_ID`       String COMMENT 'FT:  HANDLER, CP: PROBER, 取自STDF文件的HAND_ID' CODEC(ZSTD(1)),
    `PROBECARD_LOADBOARD_TYP` String COMMENT 'CP: PROBECARD，取CARD_TYP FT:LOADBOARD，取LOAD_TYPE' CODEC(ZSTD(1)),
    `PROBECARD_LOADBOARD_ID`  String COMMENT 'CP: CARD_ID, FT: LOAD_ID' CODEC(ZSTD(1)),
    `PART_FLG`                String COMMENT 'PART INFORMATION FLAG，STDF' CODEC(ZSTD(1)),
    `PART_ID`                 String CODEC(ZSTD(1)),
    `C_PART_ID` Nullable(UInt32) COMMENT '基于STDF的PARTID计算的自定义PARTID' CODEC(Delta, ZSTD),
    `UID`                     String COMMENT 'uid' CODEC(ZSTD(1)),
    `ECID`                    String COMMENT 'ELECTRONIC CHIP ID' CODEC(ZSTD(1)),
    `ECID_EXT`                String COMMENT 'ELECTRONIC CHIP ID扩展信息，用于存储FT阶段多CHIPID封装测试' CODEC(ZSTD(1)),
    `ECID_EXTRA` Map(String, String) COMMENT 'ELECTRONIC CHIP ID扩展信息，用于存储FT阶段多CHIPID封装测试,Map动态类型{KEY: VALUE}' CODEC(ZSTD(1)),
    `IS_STANDARD_ECID` Nullable(UInt8) COMMENT '是否标准的ECID: 0/1' CODEC(Delta, ZSTD),
    `X_COORD` Nullable(Int32) COMMENT '原始STDF的X坐标' CODEC(Delta, ZSTD),
    `Y_COORD` Nullable(Int32) COMMENT '原始STDF的Y坐标' CODEC(Delta, ZSTD),
    `DIE_X` Nullable(Int32) COMMENT '转换后的X坐标，Notch为D，POS_X为R，POS_Y为D' CODEC(Delta, ZSTD),
    `DIE_Y` Nullable(Int32) COMMENT '转换后的Y坐标，Notch为D，POS_X为R，POS_Y为D' CODEC(Delta, ZSTD),
    `TEST_TIME` Nullable(UInt32) COMMENT '芯片测试花费时间（MS），ELAPSED TEST TIME IN MILLISECONDS，STDF TESST_T' CODEC(Delta, ZSTD),
    `PART_TXT`                String CODEC(ZSTD(1)),
    `PART_FIX`                String CODEC(ZSTD(1)),
    `TOUCH_DOWN_ID` Nullable(UInt32) COMMENT 'touch_down_id' CODEC(Delta, ZSTD),
    `SITE` Nullable(UInt32) COMMENT '指TEST SITE NUMBER，STDF的SITE_NUM' CODEC(Delta, ZSTD),
    `SITE_GRP` Nullable(UInt32) COMMENT 'SITE GROUP，STDF的SITE_GRP' CODEC(Delta, ZSTD),
    `SITE_CNT` Nullable(UInt32) COMMENT '总共SITE个数，STDF SITE_CNT' CODEC(Delta, ZSTD),
    `SITE_NUMS`               String COMMENT '总共的SITE NUMBER, STDF SITE_NUMS' CODEC(ZSTD(1)),
    `TEXT_DAT`                String COMMENT '由DTR解析出的信息' CODEC(ZSTD(1)),
    `START_TIME` Nullable(DateTime) COMMENT '测试开始时间， STDF 取MIR_START_T' CODEC(Delta, ZSTD),
    `END_TIME` Nullable(DateTime) COMMENT '测试结束时间， STDF取MRR_FINISH_T' CODEC(Delta, ZSTD),
    `START_HOUR_KEY`          String CODEC(ZSTD(1)),
    `START_DAY_KEY`           String CODEC(ZSTD(1)),
    `END_HOUR_KEY`            String CODEC(ZSTD(1)),
    `END_DAY_KEY`             String CODEC(ZSTD(1)),
    `WAFER_ID`                String CODEC(ZSTD(1)),
    `WAFER_NO`                String CODEC(ZSTD(1)),
    `WAFER_SIZE` Nullable(Decimal(38, 18)) COMMENT '晶圆直径' CODEC(ZSTD(1)),
    `WAFER_MARGIN` Nullable(Decimal(38, 18)) COMMENT '晶圆edge的margin' CODEC(ZSTD(1)),
    `DIE_HEIGHT` Nullable(Decimal(38, 18)) CODEC(ZSTD(1)),
    `DIE_WIDTH` Nullable(Decimal(38, 18)) CODEC(ZSTD(1)),
    `WF_UNITS` Nullable(UInt32) COMMENT '晶圆单位：0->unknow,1->in,2->cm,3->mm,4->m' CODEC(Delta, ZSTD),
    `WF_FLAT`                 String CODEC(ZSTD(1)),
    `CENTER_X` Nullable(Int32) COMMENT 'center die(CP)/reticle(WAT) 的x坐标' CODEC(Delta, ZSTD),
    `CENTER_Y` Nullable(Int32) COMMENT 'center die(CP)/reticle(WAT) 的y坐标' CODEC(Delta, ZSTD),
    `CENTER_OFFSET_X` Nullable(Decimal(38, 18)) COMMENT 'center x - wafer center x' CODEC(ZSTD(1)),
    `CENTER_OFFSET_Y` Nullable(Decimal(38, 18)) COMMENT 'center y - wafer center y' CODEC(ZSTD(1)),
    `CENTER_RETICLE_X` Nullable(Int32) COMMENT 'center reticle的x坐标' CODEC(Delta, ZSTD),
    `CENTER_RETICLE_Y` Nullable(UInt8) COMMENT 'center reticle的y坐标' CODEC(Delta, ZSTD),
    `CENTER_RETICLE_OFFSET_X` Nullable(Decimal(38, 18)) CODEC(ZSTD(1)),
    `CENTER_RETICLE_OFFSET_Y` Nullable(Decimal(38, 18)) CODEC(ZSTD(1)),
    `POS_X`                   String COMMENT 'X坐标轴方向' CODEC(ZSTD(1)),
    `POS_Y`                   String COMMENT 'Y坐标轴方向' CODEC(ZSTD(1)),
    `DIE_CNT` Nullable(UInt32) COMMENT 'WAFER上实际芯片个数' CODEC(Delta, ZSTD),
    `RETICLE_T_X` Nullable(Int32) COMMENT 'WAT RETICLE 转换后X坐标' CODEC(Delta, ZSTD),
    `RETICLE_T_Y` Nullable(Int32) COMMENT 'WAT RETICLE 转换后Y坐标' CODEC(Delta, ZSTD),
    `RETICLE_X` Nullable(Int32) COMMENT 'WAT RETICLE 原始X坐标' CODEC(Delta, ZSTD),
    `RETICLE_Y` Nullable(Int32) COMMENT 'WAT RETICLE 原始Y坐标' CODEC(Delta, ZSTD),
    `RETICLE_ROW` Nullable(UInt32) COMMENT 'x方向上一个reticle里面几个die' CODEC(Delta, ZSTD),
    `RETICLE_COLUMN` Nullable(UInt32) COMMENT 'y方向上一个reticle里面几个die' CODEC(Delta, ZSTD),
    `RETICLE_ROW_CENTER_OFFSET` Nullable(Int32) COMMENT 'center die在center reticle中x轴方向的offset' CODEC(Delta, ZSTD),
    `RETICLE_COLUMN_CENTER_OFFSET` Nullable(Int32) COMMENT 'center die在center reticle中y轴方向的offset' CODEC(Delta, ZSTD),
    `ORIGINAL_WAFER_SIZE` Nullable(Decimal(38, 18)) COMMENT '晶圆直径' CODEC(ZSTD(1)),
    `ORIGINAL_WAFER_MARGIN` Nullable(Decimal(38, 18)) COMMENT '晶圆edge的margin' CODEC(ZSTD(1)),
    `ORIGINAL_WF_UNITS` Nullable(UInt32) COMMENT '晶圆单位：0->unknow,1->in,2->cm,3->mm,4->m' CODEC(Delta, ZSTD),
    `ORIGINAL_WF_FLAT`        String CODEC(ZSTD(1)),
    `ORIGINAL_POS_X`          String COMMENT 'X坐标轴原始方向' CODEC(ZSTD(1)),
    `ORIGINAL_POS_Y`          String COMMENT 'Y坐标轴原始方向' CODEC(ZSTD(1)),
    `ORIGINAL_DIE_WIDTH` Nullable(Decimal(38, 18)) CODEC(ZSTD(1)),
    `ORIGINAL_DIE_HEIGHT` Nullable(Decimal(38, 18)) CODEC(ZSTD(1)),
    `ORIGINAL_RETICLE_ROW` Nullable(UInt32) COMMENT 'x方向上一个reticle里面几个die' CODEC(Delta, ZSTD),
    `ORIGINAL_RETICLE_COLUMN` Nullable(UInt32) COMMENT 'y方向上一个reticle里面几个die' CODEC(Delta, ZSTD),
    `ORIGINAL_RETICLE_ROW_CENTER_OFFSET` Nullable(Int32) COMMENT 'center die在center reticle中x轴方向的offset' CODEC(Delta, ZSTD),
    `ORIGINAL_RETICLE_COLUMN_CENTER_OFFSET` Nullable(Int32) COMMENT 'center die在center reticle中y轴方向的offset' CODEC(Delta, ZSTD),
    `ORIGINAL_CENTER_X` Nullable(Int32) COMMENT 'center die的原始x坐标' CODEC(Delta, ZSTD),
    `ORIGINAL_CENTER_Y` Nullable(Int32) COMMENT 'center die的原始y坐标' CODEC(Delta, ZSTD),
    `ORIGINAL_CENTER_RETICLE_X` Nullable(Int32) COMMENT 'center reticle的原始x坐标' CODEC(Delta, ZSTD),
    `ORIGINAL_CENTER_RETICLE_Y` Nullable(UInt8) COMMENT 'center reticle的原始y坐标' CODEC(Delta, ZSTD),
    `ORIGINAL_CENTER_OFFSET_X` Nullable(Decimal(38, 18)) COMMENT 'center x - wafer center x' CODEC(ZSTD(1)),
    `ORIGINAL_CENTER_OFFSET_Y` Nullable(Decimal(38, 18)) COMMENT 'center y - wafer center y' CODEC(ZSTD(1)),
    `ORIGINAL_CENTER_RETICLE_OFFSET_X` Nullable(Decimal(38, 18)) CODEC(ZSTD(1)),
    `ORIGINAL_CENTER_RETICLE_OFFSET_Y` Nullable(Decimal(38, 18)) CODEC(ZSTD(1)),
    `SITE_ID`                 String COMMENT 'WAT数据的SITE_ID' CODEC(ZSTD(1)),
    `PART_CNT` Nullable(UInt32) CODEC(Delta, ZSTD),
    `RTST_CNT` Nullable(UInt32) CODEC(Delta, ZSTD),
    `ABRT_CNT` Nullable(UInt32) CODEC(Delta, ZSTD),
    `GOOD_CNT` Nullable(UInt32) CODEC(Delta, ZSTD),
    `FUNC_CNT` Nullable(UInt32) CODEC(Delta, ZSTD),
    `FABWF_ID`                String CODEC(ZSTD(1)),
    `FRAME_ID`                String CODEC(ZSTD(1)),
    `MASK_ID`                 String CODEC(ZSTD(1)),
    `WAFER_USR_DESC`          String CODEC(ZSTD(1)),
    `WAFER_EXC_DESC`          String CODEC(ZSTD(1)),
    `SETUP_T` Nullable(DateTime) CODEC(Delta, ZSTD),
    `STAT_NUM` Nullable(UInt32) CODEC(Delta, ZSTD),
    `MODE_COD`                String CODEC(ZSTD(1)),
    `PROT_COD`                String CODEC(ZSTD(1)),
    `BURN_TIM` Nullable(UInt32) CODEC(Delta, ZSTD),
    `CMOD_COD`                String CODEC(ZSTD(1)),
    `EXEC_TYP`                String CODEC(ZSTD(1)),
    `EXEC_VER`                String CODEC(ZSTD(1)),
    `USER_TXT`                String CODEC(ZSTD(1)),
    `AUX_FILE`                String CODEC(ZSTD(1)),
    `PKG_TYP`                 String CODEC(ZSTD(1)),
    `FAMLY_ID`                String CODEC(ZSTD(1)),
    `DATE_COD`                String CODEC(ZSTD(1)),
    `FACIL_ID`                String CODEC(ZSTD(1)),
    `FLOOR_ID`                String CODEC(ZSTD(1)),
    `PROC_ID`                 String CODEC(ZSTD(1)),
    `OPER_FRQ`                String CODEC(ZSTD(1)),
    `FLOW_ID`                 String CODEC(ZSTD(1)),
    `FLOW_ID_IGNORE_TP`       String COMMENT '忽略测试程序，flowId' CODEC(ZSTD(1)),
    `SETUP_ID`                String CODEC(ZSTD(1)),
    `DSGN_REV`                String CODEC(ZSTD(1)),
    `ENG_ID`                  String CODEC(ZSTD(1)),
    `ROM_COD`                 String CODEC(ZSTD(1)),
    `SERL_NUM`                String CODEC(ZSTD(1)),
    `SUPR_NAM`                String CODEC(ZSTD(1)),
    `DISP_COD`                String CODEC(ZSTD(1)),
    `LOT_USR_DESC`            String CODEC(ZSTD(1)),
    `LOT_EXC_DESC`            String CODEC(ZSTD(1)),
    `DIB_TYP`                 String CODEC(ZSTD(1)),
    `DIB_ID`                  String CODEC(ZSTD(1)),
    `CABL_TYP`                String CODEC(ZSTD(1)),
    `CABL_ID`                 String CODEC(ZSTD(1)),
    `CONT_TYP`                String CODEC(ZSTD(1)),
    `CONT_ID`                 String CODEC(ZSTD(1)),
    `LASR_TYP`                String CODEC(ZSTD(1)),
    `LASR_ID`                 String CODEC(ZSTD(1)),
    `EXTR_TYP`                String CODEC(ZSTD(1)),
    `EXTR_ID`                 String CODEC(ZSTD(1)),
    `EFUSE_EXTRA`             Map(String, String) default map() CODEC(ZSTD(1)),
    `CHIP_ID`                 String default '' CODEC(ZSTD(1)),
    `RETEST_BIN_NUM`          String CODEC(ZSTD(1)),
    `VECT_NAM`                String COMMENT 'FTR特有的字段' CODEC(ZSTD(1)),
    `TIME_SET`                String COMMENT 'FTR特有的字段' CODEC(ZSTD(1)),
    `NUM_FAIL` Nullable(UInt32) COMMENT 'FTR特有的字段' CODEC(Delta, ZSTD),
    `FAIL_PIN`                String COMMENT 'FTR特有的字段' CODEC(ZSTD(1)),
    `CYCL_CNT` Nullable(UInt32) COMMENT 'FTR特有的字段' CODEC(Delta, ZSTD),
    `REPT_CNT` Nullable(UInt32) COMMENT 'FTR特有的字段' CODEC(Delta, ZSTD),
    `LONG_ATTRIBUTE_SET` Map(String, Int64) COMMENT '其他属性字段, 用于Long/Int类型字段扩展   Map动态类型{KEY: VALUE}' CODEC(ZSTD(1)),
    `STRING_ATTRIBUTE_SET` Map(String, String) COMMENT '其他属性字段, 用于字符类型字段扩展   Map动态类型{KEY: VALUE}' CODEC(ZSTD(1)),
    `FLOAT_ATTRIBUTE_SET` Map(String, Decimal(38, 18)) COMMENT '其他属性字段, 用于数值类型字段扩展   Map动态类型{KEY: VALUE}' CODEC(ZSTD(1)),
    `CREATE_HOUR_KEY`         String CODEC(ZSTD(1)),
    `CREATE_DAY_KEY`          String CODEC(ZSTD(1)),
    `CREATE_TIME`             DateTime COMMENT '创建时间，系统字段' CODEC(Delta, ZSTD),
    `CREATE_USER`             String COMMENT '创建用户，系统字段' CODEC(ZSTD(1)),
    `UPLOAD_TIME`             DateTime default CREATE_TIME comment '数据上传时间,系统字段' CODEC(Delta, ZSTD),
    `DATA_VERSION`            Int64 default 1 comment '开始跑数仓数据的时间' CODEC(Delta, ZSTD),
    `LOT_BUCKET`              Int32 COMMENT '用于减少按LOT_ID分区的数量' CODEC(Delta, ZSTD),
    `IS_DELETE`               UInt8 DEFAULT 0 COMMENT '是否删除' CODEC(Delta, ZSTD)
) ENGINE = ReplicatedMergeTree
      PARTITION BY (CUSTOMER, UPLOAD_TYPE, TEST_AREA, FACTORY, SUB_CUSTOMER, DEVICE_ID, LOT_BUCKET)
      ORDER BY (TEST_ITEM, LOT_ID, WAFER_ID, FILE_ID)
      SETTINGS index_granularity = 8192, max_parts_in_total=1000000, max_bytes_to_merge_at_max_space_in_pool = 10737418240;