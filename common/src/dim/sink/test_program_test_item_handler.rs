use crate::ck::ck_sink::{<PERSON><PERSON><PERSON><PERSON><PERSON>, CkSink};
use ck_provider::CkConfig;
use clickhouse::Row;
use serde::Serialize;
use std::error::Error;

const TABLE_NAME: &str = "dim_test_program_test_item_local";
const PARTITION_EXPR: &str = "('{CUSTOMER}', '{UPLOAD_TYPE}', '{TEST_AREA}', '{FACTORY}', '{SUB_CUSTOMER}', '{DEVICE_ID}')";

/// Sink handler for TestProgramTestItem data to ClickHouse
/// <PERSON><PERSON> writes to dim_test_program_test_item_local table
#[derive(Debug, Clone)]
pub struct TestProgramTestItemHandler {
    pub db_name: String,
    pub table_name: String,
}

impl TestProgramTestItemHandler {
    /// Creates a new TestProgramTestItemHandler
    ///
    /// # Arguments
    /// * `db_name` - The database name for the DIM layer
    pub fn new(db_name: String) -> Self {
        Self {
            db_name,
            table_name: TABLE_NAME.into()
        }
    }

    /// Generic method to write data to ClickHouse
    pub async fn write_to_ck_generic<T>(
        &self,
        data: Vec<T>,
        ck_config: CkConfig,
    ) -> Result<(), Box<dyn Error + Send + Sync>>
    where
        T: Row + Serialize + Send + Sync + Clone + 'static,
    {
        if data.is_empty() {
            log::info!("No data to write.");
            return Ok(());
        }

        log::info!("Writing {} records to ClickHouse.", data.len());

        // Write to ClickHouse
        CkSink::write_to_ck(&data, 1, &ck_config, self, false).await.map_err(
            |e| -> Box<dyn Error + Send + Sync> {
                log::error!("写入clickhouse 失败: {}, 数据量为: {}", self.table_name(), data.len());
                Box::new(std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))
            },
        )?;

        log::info!("Successfully wrote {} records to ClickHouse.", data.len());
        Ok(())
    }
}

impl SinkHandler for TestProgramTestItemHandler {
    fn db_name(&self) -> &str {
        &self.db_name
    }

    fn table_name(&self) -> &str {
        &self.table_name
    }

    fn partition_expr(&self) -> &str {
        PARTITION_EXPR
    }
}
