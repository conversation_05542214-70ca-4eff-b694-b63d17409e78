#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum DwTableCalculateStep {
    CalculateStart,
    CalculateEnd,
    SinkCkStart,
    SinkCkEnd,
}

impl DwTableCalculateStep {
    pub fn as_str(&self) -> &'static str {
        match self {
            DwTableCalculateStep::CalculateStart => "CALCULATE_START",
            DwTableCalculateStep::CalculateEnd => "CALCULATE_END",
            DwTableCalculateStep::SinkCkStart => "SINK_CK_START",
            DwTableCalculateStep::SinkCkEnd => "SINK_CK_END",
        }
    }
    
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "CALCULATE_START" => Some(DwTableCalculateStep::CalculateStart),
            "CALCULATE_END" => Some(DwTableCalculateStep::CalculateEnd),
            "SINK_CK_START" => Some(DwTableCalculateStep::SinkCkStart),
            "SINK_CK_END" => Some(DwTableCalculateStep::SinkCkEnd),
            _ => None,
        }
    }
}