//! DWS模式枚举
//!
//! 定义数据仓库DWS层的计算模式

use std::collections::HashMap;
use std::fmt;

/// DWS模式枚举
#[derive(Debug, <PERSON>lone, PartialEq, Eq, Hash)]
pub enum DwsMode {
    /// DWS标准模式
    DWS,
    /// MES DWS补丁模式
    MesDwsPatch,
}

impl DwsMode {
    /// 获取模式字符串值
    pub fn get_mode(&self) -> &'static str {
        match self {
            DwsMode::DWS => "DWS",
            DwsMode::MesDwsPatch => "MES_DWS_PATCH",
        }
    }

    /// 从字符串创建DwsMode
    pub fn of(mode: &str) -> Option<DwsMode> {
        match mode {
            "DWS" => Some(DwsMode::DWS),
            "MES_DWS_PATCH" => Some(DwsMode::MesDwsPatch),
            _ => None,
        }
    }

    /// 获取所有模式的映射
    pub fn mode_map() -> HashMap<String, DwsMode> {
        let mut map = HashMap::new();
        map.insert("DWS".to_string(), DwsMode::DWS);
        map.insert("MES_DWS_PATCH".to_string(), DwsMode::MesDwsPatch);
        map
    }
}

impl fmt::Display for DwsMode {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.get_mode())
    }
}

impl From<&str> for DwsMode {
    fn from(s: &str) -> Self {
        DwsMode::of(s).unwrap_or(DwsMode::DWS)
    }
}

