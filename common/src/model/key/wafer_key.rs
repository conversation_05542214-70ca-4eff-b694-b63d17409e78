use serde::{Deserialize, Serialize};
use std::fmt;

use crate::model::key::<PERSON><PERSON><PERSON>;

/// Wafer<PERSON>ey represents the unique identifier for a wafer in the data processing pipeline
/// Corresponds to: Wafer<PERSON><PERSON> case class in Scala implementation
/// This structure is used to identify unique wafer processing contexts and for path generation
#[derive(<PERSON>bug, <PERSON><PERSON>, Hash, PartialEq, Eq, Serialize, Deserialize)]
pub struct WaferKey {
    /// Customer name - Corresponds to Scala WaferKey.customer
    pub customer: String,

    /// Sub-customer name - Corresponds to Scala WaferKey.subCustomer
    pub sub_customer: String,

    /// Factory name - Corresponds to Scala WaferKey.factory
    pub factory: String,

    /// Factory site location - Corresponds to Scala WaferKey.factorySite
    pub factory_site: String,

    /// Test area (CP, FT, etc.) - Corresponds to Scala WaferKey.testArea
    pub test_area: String,

    /// Test stage within the test area - Corresponds to Scala WaferKey.testStage
    pub test_stage: String,

    /// Device identifier - Corresponds to Scala WaferKey.deviceId
    pub device_id: String,

    /// Lot type (PRODUCTION, ENGINEERING, etc.) - Corresponds to Scala WaferKey.lotType
    pub lot_type: String,

    /// Lot identifier - Corresponds to Scala WaferKey.lotId
    pub lot_id: String,

    /// Wafer number within the lot - Corresponds to Scala WaferKey.waferNo
    pub wafer_no: String,
}

impl WaferKey {
    /// Create a new WaferKey instance
    /// Corresponds to: WaferKey case class constructor in Scala
    pub fn new(
        customer: String,
        sub_customer: String,
        factory: String,
        factory_site: String,
        test_area: String,
        test_stage: String,
        device_id: String,
        lot_type: String,
        lot_id: String,
        wafer_no: String,
    ) -> Self {
        Self {
            customer,
            sub_customer,
            factory,
            factory_site,
            test_area,
            test_stage,
            device_id,
            lot_type,
            lot_id,
            wafer_no,
        }
    }

    /// Generate a string representation for logging and debugging
    /// Corresponds to: WaferKey.toString in Scala
    pub fn to_string_key(&self) -> String {
        format!(
            "{}_{}_{}_{}_{}_{}_{}_{}_{}_{}",
            self.customer,
            self.sub_customer,
            self.factory,
            self.factory_site,
            self.test_area,
            self.test_stage,
            self.device_id,
            self.lot_type,
            self.lot_id,
            self.wafer_no
        )
    }

    pub fn to_lot_key(&self) -> LotKey {
        LotKey::new(
            self.customer.clone(),
            self.sub_customer.clone(),
            self.test_area.clone(),
            self.factory.clone(),
            self.factory_site.clone(),
            self.device_id.clone(),
            self.lot_id.clone(),
            self.test_stage.clone(),
            self.lot_type.clone(),
        )
    }

    /// Generate a path-safe string representation
    /// Used for HDFS path generation - Corresponds to path building logic in Scala
    pub fn to_path_string(&self) -> String {
        format!(
            "TEST_AREA={}/CUSTOMER={}/FACTORY={}/DEVICE_ID={}/LOT_ID={}/WAFER_NO={}/TEST_STAGE={}/LOT_TYPE={}",
            self.test_area,
            self.customer,
            self.factory,
            self.device_id,
            self.lot_id,
            self.wafer_no,
            self.test_stage,
            self.lot_type
        )
    }
}

impl fmt::Display for WaferKey {
    /// Display implementation for logging
    /// Corresponds to: WaferKey logging in Scala (line 33: "当前正在计算的waferNo: " + waferKey)
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "WaferKey[customer={}, factory={}, device_id={}, lot_id={}, wafer_no={}, test_stage={}, lot_type={}]",
            self.customer, self.factory, self.device_id, self.lot_id, self.wafer_no, self.test_stage, self.lot_type
        )
    }
}

impl Default for WaferKey {
    fn default() -> Self {
        Self {
            customer: String::new(),
            sub_customer: String::new(),
            factory: String::new(),
            factory_site: String::new(),
            test_area: String::new(),
            test_stage: String::new(),
            device_id: String::new(),
            lot_type: String::new(),
            lot_id: String::new(),
            wafer_no: String::new(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    #[test]
    fn test_wafer_key_creation() {
        let wafer_key = WaferKey::new(
            "YEESTOR".to_string(),
            "YEESTOR_SUB".to_string(),
            "LEADYO".to_string(),
            "LEADYO_SITE".to_string(),
            "CP".to_string(),
            "CP1".to_string(),
            "YS8293ENAB".to_string(),
            "PRODUCTION".to_string(),
            "ENF083".to_string(),
            "25".to_string(),
        );

        assert_eq!(wafer_key.customer, "YEESTOR");
        assert_eq!(wafer_key.lot_id, "ENF083");
        assert_eq!(wafer_key.wafer_no, "25");
    }

    #[test]
    fn test_wafer_key_hash_map_usage() {
        let wafer_key1 = WaferKey::new(
            "YEESTOR".to_string(),
            "YEESTOR_SUB".to_string(),
            "LEADYO".to_string(),
            "LEADYO_SITE".to_string(),
            "CP".to_string(),
            "CP1".to_string(),
            "YS8293ENAB".to_string(),
            "PRODUCTION".to_string(),
            "ENF083".to_string(),
            "25".to_string(),
        );

        let wafer_key2 = wafer_key1.clone();

        let mut map = HashMap::new();
        map.insert(wafer_key1, "test_value");

        assert!(map.contains_key(&wafer_key2));
        assert_eq!(map.get(&wafer_key2), Some(&"test_value"));
    }

    #[test]
    fn test_wafer_key_display() {
        let wafer_key = WaferKey::new(
            "YEESTOR".to_string(),
            "YEESTOR_SUB".to_string(),
            "LEADYO".to_string(),
            "LEADYO_SITE".to_string(),
            "CP".to_string(),
            "CP1".to_string(),
            "YS8293ENAB".to_string(),
            "PRODUCTION".to_string(),
            "ENF083".to_string(),
            "25".to_string(),
        );

        let display_str = format!("{}", wafer_key);
        assert!(display_str.contains("YEESTOR"));
        assert!(display_str.contains("ENF083"));
        assert!(display_str.contains("25"));
    }

    #[test]
    fn test_wafer_key_path_string() {
        let wafer_key = WaferKey::new(
            "YEESTOR".to_string(),
            "YEESTOR_SUB".to_string(),
            "LEADYO".to_string(),
            "LEADYO_SITE".to_string(),
            "CP".to_string(),
            "CP1".to_string(),
            "YS8293ENAB".to_string(),
            "PRODUCTION".to_string(),
            "ENF083".to_string(),
            "25".to_string(),
        );

        let path_str = wafer_key.to_path_string();
        assert!(path_str.contains("TEST_AREA=CP"));
        assert!(path_str.contains("CUSTOMER=YEESTOR"));
        assert!(path_str.contains("LOT_ID=ENF083"));
        assert!(path_str.contains("WAFER_NO=25"));
    }

    #[test]
    fn test_wafer_key_string_key() {
        let wafer_key = WaferKey::new(
            "YEESTOR".to_string(),
            "YEESTOR_SUB".to_string(),
            "LEADYO".to_string(),
            "LEADYO_SITE".to_string(),
            "CP".to_string(),
            "CP1".to_string(),
            "YS8293ENAB".to_string(),
            "PRODUCTION".to_string(),
            "ENF083".to_string(),
            "25".to_string(),
        );

        let string_key = wafer_key.to_string_key();
        assert_eq!(
            string_key,
            "YEESTOR_YEESTOR_SUB_LEADYO_LEADYO_SITE_CP_CP1_YS8293ENAB_PRODUCTION_ENF083_25"
        );
    }
}
