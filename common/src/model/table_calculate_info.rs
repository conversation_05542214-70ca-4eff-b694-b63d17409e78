#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq)]
pub struct TableCalculateInfo {
    pub calculate_start_time: i64,
    pub calculate_end_time: i64,
    pub calculate_time_diff: i64,
    pub sink_ck_start_time: i64,
    pub sink_ck_end_time: i64,
    pub sink_ck_time_diff: i64,
    pub sink_db: String,
}

impl TableCalculateInfo {
    pub fn new(
        calculate_start_time: i64,
        calculate_end_time: i64,
        calculate_time_diff: i64,
        sink_ck_start_time: i64,
        sink_ck_end_time: i64,
        sink_ck_time_diff: i64,
        sink_db: String,
    ) -> Self {
        Self {
            calculate_start_time,
            calculate_end_time,
            calculate_time_diff,
            sink_ck_start_time,
            sink_ck_end_time,
            sink_ck_time_diff,
            sink_db,
        }
    }
}