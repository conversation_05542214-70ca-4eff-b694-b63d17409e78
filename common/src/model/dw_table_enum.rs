use crate::model::constant::dw_layer::Dw<PERSON>ayer;


#[derive(Debug, <PERSON><PERSON>, <PERSON>ialEq, Eq)]
pub struct DwTableInfo {
    pub dw_layer: DwLayer,
    pub table: String,
    pub local_table: String,
    pub cluster_table: String,
    pub dir_table_name: String,
}

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum DwTableEnum {
    // ODS
    OdsTestItemData,
    OdsDieData,
    
    // DWD
    DwdTestItemDetail,
    DwdDieDetail,
    DwdTestItemBitmemDetail,
    DwdDieBitmemDetail,
    DwdFileMap,
    
    // DIM
    DimLotRelation,
    DimLotWafer,
    DimFlowid,
    DimTestProgramTestItem,
    DimTestProgramBin,
    DimTimeHour,
    DimTestProgramSite,
    DimTimeDay,
    DimLotWaferBin,
    DimTestItem,
    DimBinFailitem,
    DimTestProgramTestOrder,
    DimSblot,
    
    // DWS
    DwsFlowIdBinIndex,
    DwsSiteTestItemIndex,
    DwsBinTestItemIndex,
    DwsWaferOverallYieldIndex,
    DwsWaferIndex,
    DwsSblotIndex,
    DwsPtsIndex,
    DwsPtsLotWaferIndex,
    DwsPtsSblotIndex,
    DwsSiteBinIndex,
    DwsTestItemIndex,
    DwsLotWaferIndex,
    DwsLotWaferDataCheckIndex,
    DwsBinIndex,
    DwsBinFailitemIndex,
    DwsSblotBinIndex,
    DwsSblotAnalogIndex,
    DwsSblotBinAnalogIndex,
    DwsMergeFileBinIndex,
    
    // ADS
    AdsBinMap,
    AdsBoxPlotSiteTestItem,
    AdsBoxPlotTestItem,
    AdsScatter,
    AdsParametricMap,
    AdsHistogram,
}

impl DwTableEnum {
    pub fn get_table_info(&self) -> DwTableInfo {
        match self {
            // ODS
            DwTableEnum::OdsTestItemData => DwTableInfo {
                dw_layer: DwLayer::ODS,
                table: "OdsTestItemData".to_string(),
                local_table: "ods_test_item_data_local".to_string(),
                cluster_table: "ods_test_item_data_cluster".to_string(),
                dir_table_name: "TEST_ITEM_DATA".to_string(),
            },
            DwTableEnum::OdsDieData => DwTableInfo {
                dw_layer: DwLayer::ODS,
                table: "OdsDieData".to_string(),
                local_table: "ods_die_data_local".to_string(),
                cluster_table: "ods_die_data_cluster".to_string(),
                dir_table_name: "DIE_DATA".to_string(),
            },
            
            // DWD
            DwTableEnum::DwdTestItemDetail => DwTableInfo {
                dw_layer: DwLayer::DWD,
                table: "DwdTestItemDetail".to_string(),
                local_table: "dwd_test_item_detail_local".to_string(),
                cluster_table: "dwd_test_item_detail_cluster".to_string(),
                dir_table_name: "test_item_detail".to_string(),
            },
            DwTableEnum::DwdDieDetail => DwTableInfo {
                dw_layer: DwLayer::DWD,
                table: "DwdDieDetail".to_string(),
                local_table: "dwd_die_detail_local".to_string(),
                cluster_table: "dwd_die_detail_cluster".to_string(),
                dir_table_name: "die_detail".to_string(),
            },
            DwTableEnum::DwdTestItemBitmemDetail => DwTableInfo {
                dw_layer: DwLayer::DWD,
                table: "DwdTestItemBitmemDetail".to_string(),
                local_table: "dwd_test_item_bitmem_detail_local".to_string(),
                cluster_table: "dwd_test_item_bitmem_detail_cluster".to_string(),
                dir_table_name: "test_item_bitmem_detail".to_string(),
            },
            DwTableEnum::DwdDieBitmemDetail => DwTableInfo {
                dw_layer: DwLayer::DWD,
                table: "DwdDieBitmemDetail".to_string(),
                local_table: "dwd_die_bitmem_detail_local".to_string(),
                cluster_table: "dwd_die_bitmem_detail_cluster".to_string(),
                dir_table_name: "die_bitmem_detail".to_string(),
            },
            DwTableEnum::DwdFileMap => DwTableInfo {
                dw_layer: DwLayer::DWD,
                table: "DwdFileMap".to_string(),
                local_table: "dwd_file_map_local".to_string(),
                cluster_table: "dwd_file_map_cluster".to_string(),
                dir_table_name: "die_file_map".to_string(),
            },
            
            // DIM
            DwTableEnum::DimLotRelation => DwTableInfo {
                dw_layer: DwLayer::DIM,
                table: "DimLotRelation".to_string(),
                local_table: "dim_lot_relation_local".to_string(),
                cluster_table: "dim_lot_relation_cluster".to_string(),
                dir_table_name: "lot_relation".to_string(),
            },
            DwTableEnum::DimLotWafer => DwTableInfo {
                dw_layer: DwLayer::DIM,
                table: "DimLotWafer".to_string(),
                local_table: "dim_lot_wafer_local".to_string(),
                cluster_table: "dim_lot_wafer_cluster".to_string(),
                dir_table_name: "lot_wafer".to_string(),
            },
            DwTableEnum::DimFlowid => DwTableInfo {
                dw_layer: DwLayer::DIM,
                table: "DimFlowid".to_string(),
                local_table: "dim_flowid_local".to_string(),
                cluster_table: "dim_flowid_cluster".to_string(),
                dir_table_name: "flowid".to_string(),
            },
            DwTableEnum::DimTestProgramTestItem => DwTableInfo {
                dw_layer: DwLayer::DIM,
                table: "DimTestProgramTestItem".to_string(),
                local_table: "dim_test_program_test_item_local".to_string(),
                cluster_table: "dim_test_program_test_item_cluster".to_string(),
                dir_table_name: "test_program_test_item".to_string(),
            },
            DwTableEnum::DimTestProgramBin => DwTableInfo {
                dw_layer: DwLayer::DIM,
                table: "DimTestProgramBin".to_string(),
                local_table: "dim_test_program_bin_local".to_string(),
                cluster_table: "dim_test_program_bin_cluster".to_string(),
                dir_table_name: "test_program_bin".to_string(),
            },
            DwTableEnum::DimTimeHour => DwTableInfo {
                dw_layer: DwLayer::DIM,
                table: "DimTimeHour".to_string(),
                local_table: "dim_time_hour_local".to_string(),
                cluster_table: "dim_time_hour_cluster".to_string(),
                dir_table_name: "time_hour".to_string(),
            },
            DwTableEnum::DimTestProgramSite => DwTableInfo {
                dw_layer: DwLayer::DIM,
                table: "DimTestProgramSite".to_string(),
                local_table: "dim_test_program_site_local".to_string(),
                cluster_table: "dim_test_program_site_cluster".to_string(),
                dir_table_name: "test_program_site".to_string(),
            },
            DwTableEnum::DimTimeDay => DwTableInfo {
                dw_layer: DwLayer::DIM,
                table: "DimTimeDay".to_string(),
                local_table: "dim_time_day_local".to_string(),
                cluster_table: "dim_time_day_cluster".to_string(),
                dir_table_name: "time_day".to_string(),
            },
            DwTableEnum::DimLotWaferBin => DwTableInfo {
                dw_layer: DwLayer::DIM,
                table: "DimLotWaferBin".to_string(),
                local_table: "dim_lot_wafer_bin_local".to_string(),
                cluster_table: "dim_lot_wafer_bin_cluster".to_string(),
                dir_table_name: "lot_wafer_bin".to_string(),
            },
            DwTableEnum::DimTestItem => DwTableInfo {
                dw_layer: DwLayer::DIM,
                table: "DimTestItem".to_string(),
                local_table: "dim_test_item_local".to_string(),
                cluster_table: "dim_test_item_cluster".to_string(),
                dir_table_name: "test_item".to_string(),
            },
            DwTableEnum::DimBinFailitem => DwTableInfo {
                dw_layer: DwLayer::DIM,
                table: "DimBinFailitem".to_string(),
                local_table: "dim_bin_failitem_local".to_string(),
                cluster_table: "dim_bin_failitem_cluster".to_string(),
                dir_table_name: "bin_failitem".to_string(),
            },
            DwTableEnum::DimTestProgramTestOrder => DwTableInfo {
                dw_layer: DwLayer::DIM,
                table: "DimTestProgramTestOrder".to_string(),
                local_table: "dim_test_program_test_order_local".to_string(),
                cluster_table: "dim_test_program_test_order_cluster".to_string(),
                dir_table_name: "test_program_test_order".to_string(),
            },
            DwTableEnum::DimSblot => DwTableInfo {
                dw_layer: DwLayer::DIM,
                table: "DimSblot".to_string(),
                local_table: "dim_sblot_local".to_string(),
                cluster_table: "dim_sblot_cluster".to_string(),
                dir_table_name: "sblot".to_string(),
            },
            
            // DWS
            DwTableEnum::DwsFlowIdBinIndex => DwTableInfo {
                dw_layer: DwLayer::DWS,
                table: "DwsFlowIdBinIndex".to_string(),
                local_table: "dws_flowid_bin_index_local".to_string(),
                cluster_table: "dws_flowid_bin_index_cluster".to_string(),
                dir_table_name: "flowid_bin_index".to_string(),
            },
            DwTableEnum::DwsSiteTestItemIndex => DwTableInfo {
                dw_layer: DwLayer::DWS,
                table: "DwsSiteTestItemIndex".to_string(),
                local_table: "dws_site_test_item_index_local".to_string(),
                cluster_table: "dws_site_test_item_index_cluster".to_string(),
                dir_table_name: "site_test_item_index".to_string(),
            },
            DwTableEnum::DwsBinTestItemIndex => DwTableInfo {
                dw_layer: DwLayer::DWS,
                table: "DwsBinTestItemIndex".to_string(),
                local_table: "dws_bin_test_item_index_local".to_string(),
                cluster_table: "dws_bin_test_item_index_cluster".to_string(),
                dir_table_name: "bin_test_item_index".to_string(),
            },
            DwTableEnum::DwsWaferOverallYieldIndex => DwTableInfo {
                dw_layer: DwLayer::DWS,
                table: "DwsWaferOverallYieldIndex".to_string(),
                local_table: "dws_wafer_overall_yield_index_local".to_string(),
                cluster_table: "dws_wafer_overall_yield_index_cluster".to_string(),
                dir_table_name: "wafer_overall_yield_index".to_string(),
            },
            DwTableEnum::DwsWaferIndex => DwTableInfo {
                dw_layer: DwLayer::DWS,
                table: "DwsWaferIndex".to_string(),
                local_table: "dws_wafer_index_local".to_string(),
                cluster_table: "dws_wafer_index_cluster".to_string(),
                dir_table_name: "wafer_index".to_string(),
            },
            DwTableEnum::DwsSblotIndex => DwTableInfo {
                dw_layer: DwLayer::DWS,
                table: "DwsSblotIndex".to_string(),
                local_table: "dws_sblot_index_local".to_string(),
                cluster_table: "dws_sblot_index_cluster".to_string(),
                dir_table_name: "sblot_index".to_string(),
            },
            DwTableEnum::DwsPtsIndex => DwTableInfo {
                dw_layer: DwLayer::DWS,
                table: "DwsPtsIndex".to_string(),
                local_table: "dws_pts_index_local".to_string(),
                cluster_table: "dws_pts_index_cluster".to_string(),
                dir_table_name: "pts_index".to_string(),
            },
            DwTableEnum::DwsPtsLotWaferIndex => DwTableInfo {
                dw_layer: DwLayer::DWS,
                table: "DwsPtsLotWaferIndex".to_string(),
                local_table: "dws_pts_lot_wafer_index_local".to_string(),
                cluster_table: "dws_pts_lot_wafer_index_cluster".to_string(),
                dir_table_name: "pts_lot_wafer_index".to_string(),
            },
            DwTableEnum::DwsPtsSblotIndex => DwTableInfo {
                dw_layer: DwLayer::DWS,
                table: "DwsPtsSblotIndex".to_string(),
                local_table: "dws_pts_sblot_index_local".to_string(),
                cluster_table: "dws_pts_sblot_index_cluster".to_string(),
                dir_table_name: "pts_sblot_index".to_string(),
            },
            DwTableEnum::DwsSiteBinIndex => DwTableInfo {
                dw_layer: DwLayer::DWS,
                table: "DwsSiteBinIndex".to_string(),
                local_table: "dws_site_bin_index_local".to_string(),
                cluster_table: "dws_site_bin_index_cluster".to_string(),
                dir_table_name: "site_bin_index".to_string(),
            },
            DwTableEnum::DwsTestItemIndex => DwTableInfo {
                dw_layer: DwLayer::DWS,
                table: "DwsTestItemIndex".to_string(),
                local_table: "dws_test_item_index_local".to_string(),
                cluster_table: "dws_test_item_index_cluster".to_string(),
                dir_table_name: "test_item_index".to_string(),
            },
            DwTableEnum::DwsLotWaferIndex => DwTableInfo {
                dw_layer: DwLayer::DWS,
                table: "DwsLotWaferIndex".to_string(),
                local_table: "dws_lot_wafer_index_local".to_string(),
                cluster_table: "dws_lot_wafer_index_cluster".to_string(),
                dir_table_name: "lot_wafer_index".to_string(),
            },
            DwTableEnum::DwsLotWaferDataCheckIndex => DwTableInfo {
                dw_layer: DwLayer::DWS,
                table: "DwsLotWaferDataCheckIndex".to_string(),
                local_table: "dws_lot_wafer_data_check_index_local".to_string(),
                cluster_table: "dws_lot_wafer_data_check_index_cluster".to_string(),
                dir_table_name: "lot_wafer_data_check_index".to_string(),
            },
            DwTableEnum::DwsBinIndex => DwTableInfo {
                dw_layer: DwLayer::DWS,
                table: "DwsBinIndex".to_string(),
                local_table: "dws_bin_index_local".to_string(),
                cluster_table: "dws_bin_index_cluster".to_string(),
                dir_table_name: "bin_index".to_string(),
            },
            DwTableEnum::DwsBinFailitemIndex => DwTableInfo {
                dw_layer: DwLayer::DWS,
                table: "DwsBinFailitemIndex".to_string(),
                local_table: "dws_bin_failitem_index_local".to_string(),
                cluster_table: "dws_bin_failitem_index_cluster".to_string(),
                dir_table_name: "bin_failitem_index".to_string(),
            },
            DwTableEnum::DwsSblotBinIndex => DwTableInfo {
                dw_layer: DwLayer::DWS,
                table: "DwsSblotBinIndex".to_string(),
                local_table: "dws_sblot_bin_index_local".to_string(),
                cluster_table: "dws_sblot_bin_index_cluster".to_string(),
                dir_table_name: "sblot_bin_index".to_string(),
            },
            DwTableEnum::DwsSblotAnalogIndex => DwTableInfo {
                dw_layer: DwLayer::DWS,
                table: "DwsSblotAnalogIndex".to_string(),
                local_table: "dws_sblot_analog_local".to_string(),
                cluster_table: "dws_sblot_analog_index_cluster".to_string(),
                dir_table_name: "sblot_analog_index".to_string(),
            },
            DwTableEnum::DwsSblotBinAnalogIndex => DwTableInfo {
                dw_layer: DwLayer::DWS,
                table: "DwsSblotBinAnalogIndex".to_string(),
                local_table: "dws_sblot_bin_analog_index_local".to_string(),
                cluster_table: "dws_sblot_bin_analog_index_cluster".to_string(),
                dir_table_name: "sblot_bin_analog_index".to_string(),
            },
            DwTableEnum::DwsMergeFileBinIndex => DwTableInfo {
                dw_layer: DwLayer::DWS,
                table: "DwsMergeFileBinIndex".to_string(),
                local_table: "dws_merge_file_bin_index_local".to_string(),
                cluster_table: "dws_merge_file_bin_index_cluster".to_string(),
                dir_table_name: "merge_file_bin_index".to_string(),
            },
            
            // ADS
            DwTableEnum::AdsBinMap => DwTableInfo {
                dw_layer: DwLayer::ADS,
                table: "AdsBinMap".to_string(),
                local_table: "ads_bin_map_local".to_string(),
                cluster_table: "ads_bin_map_cluster".to_string(),
                dir_table_name: "bin_map".to_string(),
            },
            DwTableEnum::AdsBoxPlotSiteTestItem => DwTableInfo {
                dw_layer: DwLayer::ADS,
                table: "AdsBoxPlotSiteTestItem".to_string(),
                local_table: "ads_box_plot_site_test_item_local".to_string(),
                cluster_table: "ads_box_plot_site_test_item_cluster".to_string(),
                dir_table_name: "box_plot_site_test_item".to_string(),
            },
            DwTableEnum::AdsBoxPlotTestItem => DwTableInfo {
                dw_layer: DwLayer::ADS,
                table: "AdsBoxPlotTestItem".to_string(),
                local_table: "ads_box_plot_test_item_local".to_string(),
                cluster_table: "ads_box_plot_test_item_cluster".to_string(),
                dir_table_name: "box_plot_test_item".to_string(),
            },
            DwTableEnum::AdsScatter => DwTableInfo {
                dw_layer: DwLayer::ADS,
                table: "AdsScatter".to_string(),
                local_table: "ads_scatter_local".to_string(),
                cluster_table: "ads_scatter_cluster".to_string(),
                dir_table_name: "scatter".to_string(),
            },
            DwTableEnum::AdsParametricMap => DwTableInfo {
                dw_layer: DwLayer::ADS,
                table: "AdsParametricMap".to_string(),
                local_table: "ads_parametric_map_local".to_string(),
                cluster_table: "ads_parametric_map_cluster".to_string(),
                dir_table_name: "parametric_map".to_string(),
            },
            DwTableEnum::AdsHistogram => DwTableInfo {
                dw_layer: DwLayer::ADS,
                table: "AdsHistogram".to_string(),
                local_table: "ads_histogram_local".to_string(),
                cluster_table: "ads_histogram_cluster".to_string(),
                dir_table_name: "histogram".to_string(),
            },
        }
    }
    
    pub fn get_dw_layer(&self) -> DwLayer {
        self.get_table_info().dw_layer
    }
    
    pub fn get_table(&self) -> String {
        self.get_table_info().table
    }
    
    pub fn get_local_table(&self) -> String {
        self.get_table_info().local_table
    }
    
    pub fn get_cluster_table(&self) -> String {
        self.get_table_info().cluster_table
    }
    
    pub fn get_dir_table_name(&self) -> String {
        self.get_table_info().dir_table_name
    }
}