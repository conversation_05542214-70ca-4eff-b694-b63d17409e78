use crate::ck::ck_sink::Sink<PERSON>and<PERSON>;

const TABLE_NAME: &str = "ads_yms_stage_test_item_program_cluster";
const PARTITION_EXPR: &str =
    "('{DATA_SOURCE}', '{CUSTOMER}', '{UPLOAD_TYPE}', '{TEST_AREA}', '{FACTORY}', '{SUB_CUSTOMER}', '{DEVICE_ID}')";

/// Sink handler for TestItemProgram data to ClickHouse
/// <PERSON><PERSON> writes to ads_yms_stage_test_item_program_cluster table
#[derive(Debug, Clone)]
pub struct TestItemProgramHandler {
    pub db_name: String,
    pub table_name: String,
}

impl TestItemProgramHandler {
    /// Creates a new TestItemProgramHandler
    ///
    /// # Arguments
    /// * `db_name` - The database name for the ADS layer
    /// * `insert_cluster_table` - Whether to use cluster table (_cluster) or local table (_local)
    pub fn new(db_name: String) -> Self {
        Self { db_name, table_name: TABLE_NAME.into() }
    }
}

impl SinkHandler for TestItemProgramHandler {
    fn db_name(&self) -> &str {
        &self.db_name
    }

    fn table_name(&self) -> &str {
        &self.table_name
    }

    fn partition_expr(&self) -> &str {
        PARTITION_EXPR
    }
}

