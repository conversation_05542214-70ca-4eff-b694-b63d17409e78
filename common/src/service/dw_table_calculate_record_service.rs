use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};
use mysql_provider::{MySqlConfig, MySqlProviderError};
use crate::model::constant::dw_layer::DwLayer;
use crate::model::constant::upload_type::UploadType;
use crate::model::dw_table_enum::{DwTableEnum};
use crate::model::dw_table_calculate_step::DwTableCalculateStep;
use crate::model::table_calculate_info::TableCalculateInfo;
use crate::dws::model::mysql::dw_layer_calculate_pool::DwLayerCalculatePool;
use crate::dws::model::mysql::dw_table_calculate_record::DwTableCalculateRecord;
use crate::repository::mysql::dw_table_calculate_record_repository::DwTableCalculateRecordRepository;
use crate::repository::mysql::sftp_file_detail_repository::SftpFileDetailRepository;
use crate::repository::mysql::lot_stocking_detail_repository::{LotStockingDetailRepository, LotData};


/// DW Table Calculate Record Service
/// Corresponds to: DwTableCalculateRecordService.scala
pub struct DwTableCalculateRecordService {
    upload_type: UploadType,
    file_id: Option<i64>,
    file_name: String,
}

impl DwTableCalculateRecordService {
    /// Create new service instance
    /// Corresponds to: DwTableCalculateRecordService constructor in Scala
    pub fn new(upload_type: UploadType, file_id: Option<i64>, file_name: String) -> Self {
        Self {
            upload_type,
            file_id,
            file_name,
        }
    }

    /// Create service with default values
    pub fn default() -> Self {
        Self {
            upload_type: UploadType::AUTO,
            file_id: None,
            file_name: String::new(),
        }
    }

    /// Update DW table calculate time tracking
    /// Corresponds to: updateDwTableCalculate method in Scala
    pub fn update_dw_table_calculate(
        &self,
        calculate_time_map: &mut HashMap<DwTableEnum, TableCalculateInfo>,
        dw_table_enum: DwTableEnum,
        dw_table_calculate_step: DwTableCalculateStep,
        sink_db: Option<String>,
    ) {
        match dw_table_calculate_step {
            DwTableCalculateStep::CalculateStart => {
                self.start_calculate(calculate_time_map, dw_table_enum, sink_db)
            }
            DwTableCalculateStep::CalculateEnd => {
                self.end_calculate(calculate_time_map, dw_table_enum, sink_db)
            }
            DwTableCalculateStep::SinkCkStart => {
                self.start_sink(calculate_time_map, dw_table_enum, sink_db)
            }
            DwTableCalculateStep::SinkCkEnd => {
                self.end_sink(calculate_time_map, dw_table_enum, sink_db)
            }
        }
    }

    /// Start calculate timing
    /// Corresponds to: startCalculate method in Scala
    fn start_calculate(
        &self,
        calculate_time_map: &mut HashMap<DwTableEnum, TableCalculateInfo>,
        dw_table_enum: DwTableEnum,
        sink_db: Option<String>,
    ) {
        let current_time = self.current_timestamp_millis();
        let table_calculate_info = TableCalculateInfo::new(
            current_time,
            0, // calculateEndTime - will be set later
            0, // calculateTimeDiff - will be calculated later
            0, // sinkCkStartTime - will be set later
            0, // sinkCkEndTime - will be set later
            0, // sinkCkTimeDiff - will be calculated later
            sink_db.unwrap_or_default(),
        );
        calculate_time_map.insert(dw_table_enum, table_calculate_info);
    }

    /// End calculate timing
    /// Corresponds to: endCalculate method in Scala
    fn end_calculate(
        &self,
        calculate_time_map: &mut HashMap<DwTableEnum, TableCalculateInfo>,
        dw_table_enum: DwTableEnum,
        sink_db: Option<String>,
    ) {
        if let Some(calculate_info) = calculate_time_map.get_mut(&dw_table_enum) {
            if let Some(db) = sink_db {
                if !db.is_empty() {
                    calculate_info.sink_db = db;
                }
            }
            calculate_info.calculate_end_time = self.current_timestamp_millis();
            calculate_info.calculate_time_diff = 
                (calculate_info.calculate_end_time - calculate_info.calculate_start_time) / 1000;
        }
    }

    /// Start sink timing
    /// Corresponds to: startSink method in Scala
    fn start_sink(
        &self,
        calculate_time_map: &mut HashMap<DwTableEnum, TableCalculateInfo>,
        dw_table_enum: DwTableEnum,
        sink_db: Option<String>,
    ) {
        if let Some(calculate_info) = calculate_time_map.get_mut(&dw_table_enum) {
            if let Some(db) = sink_db {
                if !db.is_empty() {
                    calculate_info.sink_db = db;
                }
            }
            calculate_info.sink_ck_start_time = self.current_timestamp_millis();
        }
    }

    /// End sink timing
    /// Corresponds to: endSink method in Scala
    fn end_sink(
        &self,
        calculate_time_map: &mut HashMap<DwTableEnum, TableCalculateInfo>,
        dw_table_enum: DwTableEnum,
        sink_db: Option<String>,
    ) {
        if let Some(calculate_info) = calculate_time_map.get_mut(&dw_table_enum) {
            if let Some(db) = sink_db {
                if !db.is_empty() {
                    calculate_info.sink_db = db;
                }
            }
            calculate_info.sink_ck_end_time = self.current_timestamp_millis();
            calculate_info.sink_ck_time_diff = 
                (calculate_info.sink_ck_end_time - calculate_info.sink_ck_start_time) / 1000;
        }
    }

    /// Save DW table calculate records
    /// Corresponds to: saveDwTableCalculate method in Scala
    pub async fn save_dw_table_calculate(
        &self,
        customer: &str,
        sub_customer: &str,
        factory: &str,
        factory_site: &str,
        test_area: &str,
        device_id: &str,
        lot_id: &str,
        wafer_no: &str,
        file_category: &str,
        lot_type: &str,
        test_stage: &str,
        execute_mode: &str,
        mysql_config: MySqlConfig,
        calculate_time_map: &HashMap<DwTableEnum, TableCalculateInfo>,
        dw_layer: DwLayer,
        run_mode: &str,
        upload_type: Option<UploadType>,
    ) -> Result<(), MySqlProviderError> {
        // Create default DwLayerCalculatePool (commented logic from Scala is simplified)
        let dw_layer_calculate_pool = DwLayerCalculatePool {
            layer_calculate_pool_id: None,
            run_mode: Some(run_mode.to_string()),
            num_executors: None,
            executor_cores: None,
            executor_memory: None,
            driver_memory: None,
            parallelism: None,
            extra_conf: Some(String::new()),
            warehousing_mode: Some(String::new()),
        };

        // Get file size and lot data count
        let sftp_repo = SftpFileDetailRepository::new(mysql_config.clone()).await?;
        let lot_file_size = sftp_repo.count_lot_wafer_file_size(
            customer, factory, factory_site, test_area, device_id,
            lot_id, wafer_no, test_stage, lot_type, file_category,
        ).await?;

        let lot_repo = LotStockingDetailRepository::new(mysql_config.clone()).await?;
        let lot_data_count = lot_repo.count_lot_data(
            customer, factory, factory_site, test_area, device_id,
            lot_id, wafer_no, test_stage, lot_type, file_category,
        ).await?; // Convert error to None

        let calculate_record_repo = DwTableCalculateRecordRepository::new(mysql_config).await?;

        // Process each table calculate info
        for (dw_table_enum, table_calculate_info) in calculate_time_map {
            let record = DwTableCalculateRecord {
                customer: Some(customer.to_string()),
                sub_customer: Some(sub_customer.to_string()),
                upload_type: Some(upload_type.as_ref().unwrap_or(&self.upload_type).to_string()),
                file_id: self.file_id,
                file_name: Some(self.file_name.clone()),
                test_area: Some(test_area.to_string()),
                factory: Some(factory.to_string()),
                factory_site: Some(factory_site.to_string()),
                device_id: Some(device_id.to_string()),
                lot_id: Some(lot_id.to_string()),
                wafer_no: Some(wafer_no.to_string()),
                file_category: Some(file_category.to_string()),
                lot_type: Some(lot_type.to_string()),
                test_stage: Some(test_stage.to_string()),
                dw_layer: Some(dw_layer.as_str().to_string()),
                db: Some(table_calculate_info.sink_db.clone()),
                local_name: Some(dw_table_enum.get_local_table()),
                cluster_name: Some(dw_table_enum.get_cluster_table()),
                calculate_start_time: Some(table_calculate_info.calculate_start_time),
                calculate_end_time: Some(table_calculate_info.calculate_end_time),
                calculate_time_diff: Some(table_calculate_info.calculate_time_diff),
                sink_ck_start_time: Some(table_calculate_info.sink_ck_start_time),
                sink_ck_end_time: Some(table_calculate_info.sink_ck_end_time),
                sink_ck_time_diff: Some(table_calculate_info.sink_ck_time_diff),
                warehousing_mode: dw_layer_calculate_pool.warehousing_mode.clone(),
                file_size: Some(lot_file_size),
                die_data_count: lot_data_count.die_data_count.map(|v| v as i32),
                test_item_data_count: lot_data_count.test_item_data_count,
                run_mode: dw_layer_calculate_pool.run_mode.clone(),
                layer_calculate_pool_id: dw_layer_calculate_pool.layer_calculate_pool_id,
                num_executors: dw_layer_calculate_pool.num_executors,
                executor_cores: dw_layer_calculate_pool.executor_cores,
                executor_memory: dw_layer_calculate_pool.executor_memory,
                driver_memory: dw_layer_calculate_pool.driver_memory,
                parallelism: dw_layer_calculate_pool.parallelism,
                extra_conf: dw_layer_calculate_pool.extra_conf.clone(),
            };

            calculate_record_repo.insert_dw_table_calculate_record(&record).await?;
        }

        Ok(())
    }

    /// Get current timestamp in milliseconds
    /// Corresponds to: System.currentTimeMillis in Scala
    fn current_timestamp_millis(&self) -> i64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis() as i64
    }
}