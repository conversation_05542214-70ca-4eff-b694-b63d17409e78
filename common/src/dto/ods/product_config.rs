use serde::{Deserialize, Serialize};
use std::sync::Arc;
use clickhouse::Row;

/// ODS Product Configuration structure
/// Contains product information for aggregation
/// Corresponds to the Scala OdsProductConfig case class
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Row)]
#[allow(non_snake_case)]
pub struct OdsProductConfig {
    pub DATA_SOURCE: Arc<str>,
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub PRODUCT: Arc<str>,
    pub PRODUCT_TYPE: Arc<str>,
    pub PRODUCT_FAMILY: Arc<str>,
}

impl OdsProductConfig {
    pub fn new() -> Self {
        Self {
            DATA_SOURCE: Arc::from(""),
            CUSTOMER: Arc::from(""),
            SUB_CUSTOMER: Arc::from(""),
            FACTORY: Arc::from(""),
            FACTORY_SITE: Arc::from(""),
            TEST_AREA: Arc::from(""),
            TEST_STAGE: Arc::from(""),
            DEVICE_ID: Arc::from(""),
            PRODUCT: Arc::from(""),
            PRODUCT_TYPE: Arc::from(""),
            PRODUCT_FAMILY: Arc::from(""),
        }
    }
}

impl Default for OdsProductConfig {
    fn default() -> Self {
        Self::new()
    }
}
