use clickhouse::Row;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Row, Debug, Clone, Serialize, Deserialize, PartialEq)]
#[allow(non_snake_case)]
pub struct TestProgramTestOrder {
    pub CUSTOMER: Option<String>,
    pub SUB_CUSTOMER: Option<String>,
    pub UPLOAD_TYPE: Option<String>,
    pub FACTORY: Option<String>,
    pub FACTORY_SITE: Option<String>,
    pub FAB: Option<String>,
    pub FAB_SITE: Option<String>,
    pub TEST_AREA: Option<String>,
    pub TEST_STAGE: Option<String>,
    pub DEVICE_ID: Option<String>,
    pub LOT_TYPE: Option<String>,
    pub TEST_PROGRAM: Option<String>,
    pub TEST_PROGRAM_VERSION: Option<String>,
    pub TEST_NUM: Option<i64>,
    pub TEST_TXT: Option<String>,
    pub TEST_ITEM: Option<String>,
    pub TESTITEM_TYPE: Option<String>,
    pub TEST_ORDER: Option<i64>,
    pub EXTRA_INFO: Option<HashMap<String, String>>,
    pub CREATE_HOUR_KEY: Option<String>,
    pub CREATE_DAY_KEY: Option<String>,
    pub CREATE_TIME: Option<i64>,
    pub CREATE_USER: Option<String>,
    pub UPLOAD_TIME: Option<i64>,
}

impl TestProgramTestOrder {
    pub fn new() -> Self {
        Self {
            CUSTOMER: None,
            SUB_CUSTOMER: None,
            UPLOAD_TYPE: None,
            FACTORY: None,
            FACTORY_SITE: None,
            FAB: None,
            FAB_SITE: None,
            TEST_AREA: None,
            TEST_STAGE: None,
            DEVICE_ID: None,
            LOT_TYPE: None,
            TEST_PROGRAM: None,
            TEST_PROGRAM_VERSION: None,
            TEST_NUM: None,
            TEST_TXT: None,
            TEST_ITEM: None,
            TESTITEM_TYPE: None,
            TEST_ORDER: None,
            EXTRA_INFO: None,
            CREATE_HOUR_KEY: None,
            CREATE_DAY_KEY: None,
            CREATE_TIME: None,
            CREATE_USER: None,
            UPLOAD_TIME: None,
        }
    }
}