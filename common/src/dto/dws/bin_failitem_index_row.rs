use clickhouse::Row;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use chrono::{DateTime, Utc};
use super::bin_failitem_index::BinFailitemIndex;

#[derive(Row, Debug, Clone, Serialize, Deserialize, PartialEq)]
#[allow(non_snake_case)]
pub struct BinFailitemIndexRow {
    pub CUSTOMER: String,
    pub SUB_CUSTOMER: String,
    pub UPLOAD_TYPE: String,
    pub FACTORY: String,
    pub FACTORY_SITE: String,
    pub FAB: String,
    pub FAB_SITE: String,
    pub TEST_AREA: String,
    pub TEST_STAGE: String,
    pub LOT_TYPE: String,
    pub DEVICE_ID: String,
    pub LOT_ID: String,
    pub PROCESS: String,
    pub SBLOT_ID: String,
    pub WAFER_LOT_ID: String,
    pub WAFER_ID: String,
    pub WAFER_ID_KEY: String,
    pub WAFER_NO: String,
    pub WAFER_NO_KEY: String,
    pub TEST_PROGRAM: String,
    pub TEST_TEMPERATURE: String,
    pub TEST_PROGRAM_VERSION: String,
    pub FILE_ID: u32,
    pub FILE_NAME: String,
    pub FILE_TYPE: String,
    pub TESTER_NAME: String,
    pub TESTER_TYPE: String,
    pub PROBER_HANDLER_ID: String,
    pub PROBECARD_LOADBOARD_ID: String,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub START_TIME: Option<DateTime<Utc>>,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub END_TIME: Option<DateTime<Utc>>,
    pub START_HOUR_KEY: String,
    pub START_DAY_KEY: String,
    pub END_HOUR_KEY: String,
    pub END_DAY_KEY: String,
    pub FLOW_ID: String,
    pub FINAL_FLAG: u8,
    pub HBIN_NAM: String,
    pub HBIN_PF: String,
    pub HBIN: String,
    pub HBIN_NUM: Option<u32>,
    pub HBIN_NUM_KEY: String,
    pub SBIN_NUM: Option<u32>,
    pub SBIN_NUM_KEY: String,
    pub SBIN_NAM: String,
    pub SBIN_PF: String,
    pub SBIN: String,
    pub FIRST_FAIL_ITEM_CNT: u32,
    pub FIRST_FAIL_ITEM_DETAIL: String,
    pub LAST_FAIL_ITEM_OF_ALL: String,
    pub ALL_FAIL_ITEM_CNT: u32,
    pub ALL_FAIL_ITEM_DETAIL: String,
    pub CREATE_HOUR_KEY: String,
    pub CREATE_DAY_KEY: String,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub CREATE_TIME: DateTime<Utc>,
    pub CREATE_USER: String,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub UPLOAD_TIME: DateTime<Utc>,
    pub VERSION: i64,
    pub IS_DELETE: u8,
}

impl BinFailitemIndexRow {
    pub fn new(bin_failitem_index: &BinFailitemIndex) -> Self {
        let now = Utc::now();
        Self {
            CUSTOMER: bin_failitem_index.CUSTOMER.clone().unwrap_or_default(),
            SUB_CUSTOMER: bin_failitem_index.SUB_CUSTOMER.clone().unwrap_or_default(),
            UPLOAD_TYPE: bin_failitem_index.UPLOAD_TYPE.clone().unwrap_or_default(),
            FACTORY: bin_failitem_index.FACTORY.clone().unwrap_or_default(),
            FACTORY_SITE: bin_failitem_index.FACTORY_SITE.clone().unwrap_or_default(),
            FAB: bin_failitem_index.FAB.clone().unwrap_or_default(),
            FAB_SITE: bin_failitem_index.FAB_SITE.clone().unwrap_or_default(),
            TEST_AREA: bin_failitem_index.TEST_AREA.clone().unwrap_or_default(),
            TEST_STAGE: bin_failitem_index.TEST_STAGE.clone().unwrap_or_default(),
            LOT_TYPE: bin_failitem_index.LOT_TYPE.clone().unwrap_or_default(),
            DEVICE_ID: bin_failitem_index.DEVICE_ID.clone().unwrap_or_default(),
            LOT_ID: bin_failitem_index.LOT_ID.clone().unwrap_or_default(),
            PROCESS: bin_failitem_index.PROCESS.clone().unwrap_or_default(),
            SBLOT_ID: bin_failitem_index.SBLOT_ID.clone().unwrap_or_default(),
            WAFER_LOT_ID: bin_failitem_index.WAFER_LOT_ID.clone().unwrap_or_default(),
            WAFER_ID: bin_failitem_index.WAFER_ID.clone().unwrap_or_default(),
            WAFER_ID_KEY: bin_failitem_index.WAFER_ID_KEY.clone().unwrap_or_default(),
            WAFER_NO: bin_failitem_index.WAFER_NO.clone().unwrap_or_default(),
            WAFER_NO_KEY: bin_failitem_index.WAFER_NO_KEY.clone().unwrap_or_default(),
            TEST_PROGRAM: bin_failitem_index.TEST_PROGRAM.clone().unwrap_or_default(),
            TEST_TEMPERATURE: bin_failitem_index.TEST_TEMPERATURE.clone().unwrap_or_default(),
            TEST_PROGRAM_VERSION: bin_failitem_index.TEST_PROGRAM_VERSION.clone().unwrap_or_default(),
            FILE_ID: bin_failitem_index.FILE_ID.unwrap_or_default() as u32,
            FILE_NAME: bin_failitem_index.FILE_NAME.clone().unwrap_or_default(),
            FILE_TYPE: bin_failitem_index.FILE_TYPE.clone().unwrap_or_default(),
            TESTER_NAME: bin_failitem_index.TESTER_NAME.clone().unwrap_or_default(),
            TESTER_TYPE: bin_failitem_index.TESTER_TYPE.clone().unwrap_or_default(),
            PROBER_HANDLER_ID: bin_failitem_index.PROBER_HANDLER_ID.clone().unwrap_or_default(),
            PROBECARD_LOADBOARD_ID: bin_failitem_index.PROBECARD_LOADBOARD_ID.clone().unwrap_or_default(),
            START_TIME: bin_failitem_index.START_TIME.and_then(|ts| DateTime::from_timestamp_millis(ts)),
            END_TIME: bin_failitem_index.END_TIME.and_then(|ts| DateTime::from_timestamp_millis(ts)),
            START_HOUR_KEY: bin_failitem_index.START_HOUR_KEY.clone().unwrap_or_default(),
            START_DAY_KEY: bin_failitem_index.START_DAY_KEY.clone().unwrap_or_default(),
            END_HOUR_KEY: bin_failitem_index.END_HOUR_KEY.clone().unwrap_or_default(),
            END_DAY_KEY: bin_failitem_index.END_DAY_KEY.clone().unwrap_or_default(),
            FLOW_ID: bin_failitem_index.FLOW_ID.clone().unwrap_or_default(),
            FINAL_FLAG: bin_failitem_index.FINAL_FLAG.unwrap_or_default() as u8,
            HBIN_NAM: bin_failitem_index.HBIN_NAM.clone().unwrap_or_default(),
            HBIN_PF: bin_failitem_index.HBIN_PF.clone().unwrap_or_default(),
            HBIN: bin_failitem_index.HBIN.clone().unwrap_or_default(),
            HBIN_NUM: bin_failitem_index.HBIN_NUM.map(|v| v as u32),
            HBIN_NUM_KEY: bin_failitem_index.HBIN_NUM_KEY.clone().unwrap_or_default(),
            SBIN_NUM: bin_failitem_index.SBIN_NUM.map(|v| v as u32),
            SBIN_NUM_KEY: bin_failitem_index.SBIN_NUM_KEY.clone().unwrap_or_default(),
            SBIN_NAM: bin_failitem_index.SBIN_NAM.clone().unwrap_or_default(),
            SBIN_PF: bin_failitem_index.SBIN_PF.clone().unwrap_or_default(),
            SBIN: bin_failitem_index.SBIN.clone().unwrap_or_default(),
            FIRST_FAIL_ITEM_CNT: bin_failitem_index.FIRST_FAIL_ITEM_CNT.unwrap_or_default() as u32,
            FIRST_FAIL_ITEM_DETAIL: bin_failitem_index.FIRST_FAIL_ITEM_DETAIL.clone().unwrap_or_default(),
            LAST_FAIL_ITEM_OF_ALL: bin_failitem_index.LAST_FAIL_ITEM_OF_ALL.clone().unwrap_or_default(),
            ALL_FAIL_ITEM_CNT: bin_failitem_index.ALL_FAIL_ITEM_CNT.unwrap_or_default() as u32,
            ALL_FAIL_ITEM_DETAIL: bin_failitem_index.ALL_FAIL_ITEM_DETAIL.clone().unwrap_or_default(),
            CREATE_HOUR_KEY: bin_failitem_index.CREATE_HOUR_KEY.clone().unwrap_or_default(),
            CREATE_DAY_KEY: bin_failitem_index.CREATE_DAY_KEY.clone().unwrap_or_default(),
            CREATE_TIME: bin_failitem_index.CREATE_TIME.and_then(|ts| DateTime::from_timestamp_millis(ts)).unwrap_or(now),
            CREATE_USER: bin_failitem_index.CREATE_USER.clone().unwrap_or_default(),
            UPLOAD_TIME: bin_failitem_index.UPLOAD_TIME.and_then(|ts| DateTime::from_timestamp_millis(ts)).unwrap_or(now),
            VERSION: bin_failitem_index.VERSION.unwrap_or(1),
            IS_DELETE: 0,
        }
    }
}

impl Default for BinFailitemIndexRow {
    fn default() -> Self {
        Self::new(&BinFailitemIndex::new())
    }
}
