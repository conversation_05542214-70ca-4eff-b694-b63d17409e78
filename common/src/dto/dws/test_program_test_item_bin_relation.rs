use crate::dto::dws::bin_key::<PERSON><PERSON><PERSON>;
use crate::dto::dws::bin_relation::BinRelation;

#[derive(Debug, Clone)]
pub struct TestProgramTestItemBinRelation {
    pub fab: Option<String>,
    pub fab_site: Option<String>,
    pub test_program: Option<String>,
    pub test_num: Option<i64>,
    pub test_txt: Option<String>,
    pub test_item: Option<String>,
    pub testitem_type: Option<String>,
    pub bin_relation: Option<std::collections::HashMap<BinKey, BinRelation>>,
}