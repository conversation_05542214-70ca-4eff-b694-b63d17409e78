use clickhouse::Row;
use serde::{Deserialize, Serialize};
use crate::parquet::RecordBatchWrapper;
use arrow::record_batch::RecordBatch;
use serde_arrow::schema::{TracingOptions, SchemaLike};
use arrow::datatypes::{Field, FieldRef};
use std::sync::Arc;

#[derive(Row, Debug, Clone, Serialize, Deserialize, PartialEq)]
#[allow(non_snake_case)]
pub struct BinFailitem {
    pub CUSTOMER: Option<String>,
    pub SUB_CUSTOMER: Option<String>,
    pub UPLOAD_TYPE: Option<String>,
    pub FACTORY: Option<String>,
    pub FACTORY_SITE: Option<String>,
    pub FAB: Option<String>,
    pub FAB_SITE: Option<String>,
    pub TEST_AREA: Option<String>,
    pub TEST_STAGE: Option<String>,
    pub DEVICE_ID: Option<String>,
    pub LOT_TYPE: Option<String>,
    pub TEST_PROGRAM: Option<String>,
    pub TEST_PROGRAM_VERSION: Option<String>,
    pub TEST_NUM: Option<i64>,
    pub TEST_TXT: Option<String>,
    pub TEST_ITEM: Option<String>,
    pub TESTITEM_TYPE: Option<String>,
    pub UNITS: Option<String>,
    pub ORIGIN_UNITS: Option<String>,
    pub HBIN_NUM: Option<i64>,
    pub HBIN_NUM_KEY: Option<String>,
    pub HBIN_NAM: Option<String>,
    pub HBIN_PF: Option<String>,
    pub HBIN: Option<String>,
    pub SBIN_NUM: Option<i64>,
    pub SBIN_NUM_KEY: Option<String>,
    pub SBIN_NAM: Option<String>,
    pub SBIN_PF: Option<String>,
    pub SBIN: Option<String>,
    pub CREATE_HOUR_KEY: Option<String>,
    pub CREATE_DAY_KEY: Option<String>,
    pub CREATE_TIME: Option<i64>,
    pub CREATE_USER: Option<String>,
    pub VERSION: Option<i64>,
    pub UPLOAD_TIME: Option<i64>,
}

impl BinFailitem {
    pub fn new() -> Self {
        Self {
            CUSTOMER: None,
            SUB_CUSTOMER: None,
            UPLOAD_TYPE: None,
            FACTORY: None,
            FACTORY_SITE: None,
            FAB: None,
            FAB_SITE: None,
            TEST_AREA: None,
            TEST_STAGE: None,
            DEVICE_ID: None,
            LOT_TYPE: None,
            TEST_PROGRAM: None,
            TEST_PROGRAM_VERSION: None,
            TEST_NUM: None,
            TEST_TXT: None,
            TEST_ITEM: None,
            TESTITEM_TYPE: None,
            UNITS: None,
            ORIGIN_UNITS: None,
            HBIN_NUM: None,
            HBIN_NUM_KEY: None,
            HBIN_NAM: None,
            HBIN_PF: None,
            HBIN: None,
            SBIN_NUM: None,
            SBIN_NUM_KEY: None,
            SBIN_NAM: None,
            SBIN_PF: None,
            SBIN: None,
            CREATE_HOUR_KEY: None,
            CREATE_DAY_KEY: None,
            CREATE_TIME: None,
            CREATE_USER: None,
            UPLOAD_TIME: None,
            VERSION: None,
        }
    }
}

impl RecordBatchWrapper for BinFailitem {
    fn from_record_batch(batch: &RecordBatch) -> Result<Vec<Self>, String>
    where
        Self: Sized,
    {
        let result: Vec<BinFailitem> = serde_arrow::from_record_batch(batch)
            .map_err(|e| format!("Failed to deserialize from RecordBatch: {}", e))?;
        Ok(result)
    }

    fn to_record_batch(data: &[Self]) -> Result<RecordBatch, String>
    where
        Self: Sized,
    {
        if data.is_empty() {
            return Err("Cannot create RecordBatch from empty data".to_string());
        }

        let fields: Vec<Arc<Field>> = Vec::<FieldRef>::from_type::<BinFailitem>(
            TracingOptions::default()
                .allow_null_fields(true)
                .map_as_struct(false)
                .strings_as_large_utf8(false),
        )
        .map_err(|e| format!("Failed to create schema from samples: {}", e))?;

        let arrays = serde_arrow::to_arrow(&fields, data)
            .map_err(|e| format!("Failed to convert to arrow arrays: {}", e))?;
        let schema = arrow::datatypes::Schema::new(fields);

        RecordBatch::try_new(Arc::new(schema), arrays)
            .map_err(|e| format!("Failed to create RecordBatch: {}", e))
    }
}