use clickhouse::Row;
use serde::{Deserialize, Serialize};
use std::sync::Arc;

/// DieFinalInfo structure for die-level final test information
/// Used to override IS_FINAL_TEST_IGNORE_TP based on ECID and C_PART_ID matching
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq, Row)]
#[allow(non_snake_case)]
pub struct DieFinalInfo {
    pub CUSTOMER: Arc<str>,
    pub FACTORY: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub LOT_ID: Arc<str>,
    pub WAFER_NO: Arc<str>,
    pub ECID: Arc<str>,
    pub C_PART_ID: Option<u32>,
    pub IS_FINAL_TEST_IGNORE_TP: Option<u8>,
}
