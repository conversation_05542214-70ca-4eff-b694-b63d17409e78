use crate::dto::dwd::file_detail::FileDetail;
use crate::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use crate::model::constant::{EMPTY, SYSTEM};
use crate::utils::decimal::Decimal38_18;
use chrono::{DateTime, Utc};
use clickhouse::Row;
use serde::{Deserialize, Serialize};
use std::sync::Arc;

/// DimTestItemRow represents a row in the dim_test_item_local ClickHouse table
/// Corresponds to the dim_test_item_local table schema
#[derive(Debug, Clone, Serialize, Deserialize, Row)]
#[allow(non_snake_case)]
pub struct DimTestItemRow {
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub FILE_ID: u32,
    pub FILE_NAME: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub LOT_ID: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub PROCESS: Arc<str>,
    pub WAFER_ID: Arc<str>,
    pub WAFER_ID_KEY: Arc<str>,
    pub WAFER_NO: Arc<str>,
    pub WAFER_NO_KEY: Arc<str>,
    pub WAFER_LOT_ID: Arc<str>,
    pub FABWF_ID: Arc<str>,
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,
    pub TESTER_NAME: Arc<str>,
    pub TESTER_TYPE: Arc<str>,
    pub OPERATOR_NAME: Arc<str>,
    pub PROBER_HANDLER_TYP: Arc<str>,
    pub PROBER_HANDLER_ID: Arc<str>,
    pub PROBECARD_LOADBOARD_TYP: Arc<str>,
    pub PROBECARD_LOADBOARD_ID: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub START_TIME: Option<DateTime<Utc>>,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub END_TIME: Option<DateTime<Utc>>,
    pub START_HOUR_KEY: Arc<str>,
    pub START_DAY_KEY: Arc<str>,
    pub END_HOUR_KEY: Arc<str>,
    pub END_DAY_KEY: Arc<str>,
    pub TEST_HEAD: Option<u32>,
    pub SITE: Option<u32>,
    pub SITE_KEY: Arc<str>,
    pub HBIN_NUM: Option<u32>,
    pub HBIN_NUM_KEY: Arc<str>,
    pub SBIN_NUM: Option<u32>,
    pub SBIN_NUM_KEY: Arc<str>,
    pub SBIN_PF: Arc<str>,
    pub SBIN_NAM: Arc<str>,
    pub HBIN_PF: Arc<str>,
    pub HBIN_NAM: Arc<str>,
    pub HBIN: Arc<str>,
    pub SBIN: Arc<str>,
    pub TEST_NUM: Option<u32>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,
    pub TESTITEM_TYPE: Arc<str>,
    pub ORIGIN_HI_LIMIT: Option<Decimal38_18>,
    pub ORIGIN_LO_LIMIT: Option<Decimal38_18>,
    pub ORIGIN_UNITS: Arc<str>,
    pub LO_LIMIT: Option<Decimal38_18>,
    pub HI_LIMIT: Option<Decimal38_18>,
    pub UNITS: Arc<str>,
    pub CONDITION_SET: Vec<(Arc<str>, Arc<str>)>,
    pub CREATE_HOUR_KEY: Arc<str>,
    pub CREATE_DAY_KEY: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub CREATE_TIME: DateTime<Utc>,
    pub CREATE_USER: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub UPLOAD_TIME: DateTime<Utc>,
    pub VERSION: i64,
    pub IS_DELETE: u8,
}

impl DimTestItemRow {
    /// Build DimTestItemRow from SubTestItemDetail and FileDetail
    /// Corresponds to buildTestItem method in TestItemCommonService.scala
    pub fn build_test_item(sub_test_item_detail: &SubTestItemDetail, file_detail: &FileDetail) -> Self {
        // Helper function to convert Option<String> to Arc<str>
        let string_value_opt = |val: Option<String>| -> Arc<str> { val.unwrap_or_else(|| String::new()).into() };

        // Helper function to convert Option<f64> to Option<Decimal38_18>
        let to_decimal =
            |val: Option<f64>| -> Option<Decimal38_18> { val.and_then(|v| Decimal38_18::try_from(v).ok()) };

        // Check if it's CP test area - corresponds to SUPPORT_CP_TEST_AREA_LIST logic
        let is_cp_test_area = Self::is_cp_test_area(&file_detail.TEST_AREA);

        // Calculate wafer_id_key and wafer_no_key based on test area
        let wafer_id_key =
            if is_cp_test_area { string_value_opt(sub_test_item_detail.WAFER_ID.clone()) } else { EMPTY.into() };

        let wafer_no_key =
            if is_cp_test_area { string_value_opt(sub_test_item_detail.WAFER_NO.clone()) } else { EMPTY.into() };

        let sblot_id = if is_cp_test_area { EMPTY.into() } else { file_detail.SBLOT_ID.clone().into() };

        // Calculate time keys
        let start_hour_key = file_detail
            .START_TIME
            .map(|dt| crate::utils::date::get_day_hour(dt))
            .unwrap_or_default();
        let start_day_key = file_detail
            .START_TIME
            .map(|dt| crate::utils::date::get_day(dt))
            .unwrap_or_default();
        let end_hour_key = file_detail
            .END_TIME
            .map(|dt| crate::utils::date::get_day_hour(dt))
            .unwrap_or_default();
        let end_day_key = file_detail
            .END_TIME
            .map(|dt| crate::utils::date::get_day(dt))
            .unwrap_or_default();

        let now = Utc::now();
        let create_hour_key = crate::utils::date::get_day_hour(now);
        let create_day_key = crate::utils::date::get_day(now);

        // Convert condition set to Arc<str> keys and values
        let condition_set = sub_test_item_detail
            .CONDITION_SET
            .as_ref()
            .map(|map| {
                map.iter()
                    .map(|(k, v)| (Arc::from(k.as_str()), Arc::from(v.as_str())))
                    .collect::<Vec<_>>()
            })
            .unwrap_or_default();

        Self {
            CUSTOMER: file_detail.CUSTOMER.clone().into(),
            SUB_CUSTOMER: file_detail.SUB_CUSTOMER.clone().into(),
            UPLOAD_TYPE: file_detail.UPLOAD_TYPE.clone().into(),
            FILE_ID: 0,              // Set to 0 as in Scala version
            FILE_NAME: EMPTY.into(), // Set to EMPTY as in Scala version
            FACTORY: file_detail.FACTORY.clone().into(),
            FACTORY_SITE: file_detail.FACTORY_SITE.clone().into(),
            FAB: file_detail.FAB.clone().into(),
            FAB_SITE: file_detail.FAB_SITE.clone().into(),
            TEST_AREA: file_detail.TEST_AREA.clone().into(),
            TEST_STAGE: file_detail.TEST_STAGE.clone().into(),
            LOT_TYPE: file_detail.LOT_TYPE.clone().into(),
            DEVICE_ID: file_detail.DEVICE_ID.clone().into(),
            LOT_ID: file_detail.LOT_ID.clone().into(),
            SBLOT_ID: sblot_id,
            PROCESS: string_value_opt(file_detail.PROCESS.clone()),
            WAFER_ID: string_value_opt(sub_test_item_detail.WAFER_ID.clone()),
            WAFER_ID_KEY: wafer_id_key,
            WAFER_NO: string_value_opt(sub_test_item_detail.WAFER_NO.clone()),
            WAFER_NO_KEY: wafer_no_key,
            WAFER_LOT_ID: string_value_opt(sub_test_item_detail.WAFER_LOT_ID.clone()),
            FABWF_ID: file_detail.FABWF_ID.clone().into(),
            TEST_PROGRAM: file_detail.TEST_PROGRAM.clone().into(),
            TEST_PROGRAM_VERSION: file_detail.TEST_PROGRAM_VERSION.clone().into(),
            TEST_TEMPERATURE: file_detail.TEST_TEMPERATURE.clone().into(),
            TESTER_NAME: file_detail.TESTER_NAME.clone().into(),
            TESTER_TYPE: file_detail.TESTER_TYPE.clone().into(),
            OPERATOR_NAME: file_detail.OPERATOR_NAME.clone().into(),
            PROBER_HANDLER_TYP: file_detail.PROBER_HANDLER_TYP.clone().into(),
            PROBER_HANDLER_ID: file_detail.PROBER_HANDLER_ID.clone().into(),
            PROBECARD_LOADBOARD_TYP: file_detail.PROBECARD_LOADBOARD_TYP.clone().into(),
            PROBECARD_LOADBOARD_ID: file_detail.PROBECARD_LOADBOARD_ID.clone().into(),
            START_TIME: file_detail.START_TIME,
            END_TIME: file_detail.END_TIME,
            START_HOUR_KEY: start_hour_key.into(),
            START_DAY_KEY: start_day_key.into(),
            END_HOUR_KEY: end_hour_key.into(),
            END_DAY_KEY: end_day_key.into(),
            TEST_HEAD: sub_test_item_detail.TEST_HEAD.map(|n| n as u32),
            SITE: sub_test_item_detail.SITE.map(|n| n as u32),
            SITE_KEY: sub_test_item_detail.SITE.map_or_else(|| EMPTY.into(), |v| v.to_string().into()),
            HBIN_NUM: sub_test_item_detail.HBIN_NUM.map(|n| n as u32),
            HBIN_NUM_KEY: sub_test_item_detail
                .HBIN_NUM
                .map_or_else(|| EMPTY.into(), |v| v.to_string().into()),
            SBIN_NUM: sub_test_item_detail.SBIN_NUM.map(|n| n as u32),
            SBIN_NUM_KEY: sub_test_item_detail
                .SBIN_NUM
                .map_or_else(|| EMPTY.into(), |v| v.to_string().into()),
            SBIN_PF: string_value_opt(sub_test_item_detail.SBIN_PF.clone()),
            SBIN_NAM: string_value_opt(sub_test_item_detail.SBIN_NAM.clone()),
            HBIN_PF: string_value_opt(sub_test_item_detail.HBIN_PF.clone()),
            HBIN_NAM: string_value_opt(sub_test_item_detail.HBIN_NAM.clone()),
            HBIN: string_value_opt(sub_test_item_detail.HBIN.clone()),
            SBIN: string_value_opt(sub_test_item_detail.SBIN.clone()),
            TEST_NUM: sub_test_item_detail.TEST_NUM.map(|n| n as u32),
            TEST_TXT: string_value_opt(sub_test_item_detail.TEST_TXT.clone()),
            TEST_ITEM: string_value_opt(sub_test_item_detail.TEST_ITEM.clone()),
            TESTITEM_TYPE: string_value_opt(sub_test_item_detail.TESTITEM_TYPE.clone()),
            ORIGIN_HI_LIMIT: to_decimal(sub_test_item_detail.ORIGIN_HI_LIMIT),
            ORIGIN_LO_LIMIT: to_decimal(sub_test_item_detail.ORIGIN_LO_LIMIT),
            ORIGIN_UNITS: string_value_opt(sub_test_item_detail.ORIGIN_UNITS.clone()),
            LO_LIMIT: to_decimal(sub_test_item_detail.LO_LIMIT),
            HI_LIMIT: to_decimal(sub_test_item_detail.HI_LIMIT),
            UNITS: string_value_opt(sub_test_item_detail.UNITS.clone()),
            CONDITION_SET: condition_set,
            CREATE_HOUR_KEY: create_hour_key.into(),
            CREATE_DAY_KEY: create_day_key.into(),
            CREATE_TIME: now,
            CREATE_USER: SYSTEM.into(),
            UPLOAD_TIME: file_detail.UPLOAD_TIME.unwrap_or(now),
            VERSION: 0, // Set to 0 initially as in Scala version
            IS_DELETE: 0,
        }
    }

    /// Check if test area is CP
    /// Corresponds to SUPPORT_CP_TEST_AREA_LIST logic in Scala
    fn is_cp_test_area(test_area: &str) -> bool {
        // This should match the SUPPORT_CP_TEST_AREA_LIST logic from Scala
        // For now, we'll use a simple check - you may need to adjust this based on your requirements
        test_area.contains("CP") || test_area.contains("cp")
    }
}
