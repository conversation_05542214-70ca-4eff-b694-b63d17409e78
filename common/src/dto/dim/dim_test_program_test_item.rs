use crate::parquet::RecordBatchWrapper;
use arrow::array::RecordBatch;
use arrow::datatypes::{Field, FieldRef};
use serde::{Deserialize, Serialize};
use serde_arrow::{schema::TracingOptions, schema::SchemaLike};
use std::collections::HashMap;
use std::sync::Arc;

/// DIM TestProgramTestItem structure for HDFS/Parquet storage
///
/// This structure is designed for HDFS/Parquet storage and uses String types
/// for better compatibility with Parquet format. It corresponds to DimTestProgramTestItemRow
/// which is optimized for ClickHouse storage using Arc<str> types.
///
/// The design follows the SiteTestItemIndex pattern where:
/// - HDFS entities use String types for Parquet compatibility
/// - ClickHouse entities use Arc<str> types for memory optimization
/// - Conversion methods enable seamless data flow between storage systems
///
/// # Usage
/// ```rust
/// // Convert from ClickHouse entity to HDFS entity
/// let hdfs_entity = DimTestProgramTestItemRow::from_hdfs_entity(&clickhouse_row);
///
/// // Use RecordBatchWrapper for Parquet operations
/// let record_batch = DimTestProgramTestItem::to_record_batch(&data)?;
/// let data = DimTestProgramTestItem::from_record_batch(&record_batch)?;
/// ```
#[derive(Debug, Clone, Serialize, Deserialize)]
#[allow(non_snake_case)]
pub struct DimTestProgramTestItem {
    // Customer and upload information
    pub CUSTOMER: String,
    pub SUB_CUSTOMER: String,
    pub UPLOAD_TYPE: String,
    pub FILE_ID: u32,

    // Factory and location information
    pub FACTORY: String,
    pub FACTORY_SITE: String,
    pub FAB: String,
    pub FAB_SITE: String,

    // Test stage information
    pub TEST_AREA: String,
    pub TEST_STAGE: String,
    pub DEVICE_ID: String,

    // Test program information
    pub TEST_PROGRAM: String,
    pub TEST_PROGRAM_VERSION: String,
    pub TEST_TEMPERATURE: String,

    // Test item information
    pub TEST_NUM: Option<u32>,
    pub TEST_NUM_KEY: String,
    pub TEST_TXT: String,
    pub TEST_ITEM: String,
    pub TESTITEM_TYPE: String,

    // Original limits
    pub ORIGIN_HI_LIMIT: Option<f64>,
    pub ORIGIN_HI_LIMIT_KEY: String,
    pub ORIGIN_LO_LIMIT: Option<f64>,
    pub ORIGIN_LO_LIMIT_KEY: String,
    pub ORIGIN_UNITS: String,

    // Processed limits
    pub LO_LIMIT: Option<f64>,
    pub LO_LIMIT_KEY: String,
    pub HI_LIMIT: Option<f64>,
    pub HI_LIMIT_KEY: String,
    pub UNITS: String,

    // Test conditions
    pub CONDITION_SET: Option<HashMap<String, String>>,
    pub CONDITION_SET_STR: String,

    // System fields
    pub CREATE_HOUR_KEY: String,
    pub CREATE_DAY_KEY: String,
    pub CREATE_TIME: i64, // Timestamp in milliseconds
    pub CREATE_USER: String,
    pub UPLOAD_TIME: i64, // Timestamp in milliseconds
    pub VERSION: u32,
    pub IS_DELETE: u8,
}

impl RecordBatchWrapper for DimTestProgramTestItem {
    fn from_record_batch(batch: &RecordBatch) -> Result<Vec<Self>, String>
    where
        Self: Sized,
    {
        let result: Vec<DimTestProgramTestItem> = serde_arrow::from_record_batch(batch)
            .map_err(|e| format!("Failed to deserialize from RecordBatch: {}", e))?;
        Ok(result)
    }

    fn to_record_batch(data: &[Self]) -> Result<RecordBatch, String>
    where
        Self: Sized,
    {
        if data.is_empty() {
            return Err("Cannot create RecordBatch from empty data".to_string());
        }

        // Use DIM layer field processing logic
        let fields: Vec<Arc<Field>> = Vec::<FieldRef>::from_type::<DimTestProgramTestItem>(
            TracingOptions::default()
                .allow_null_fields(true)
                .map_as_struct(false)
                .strings_as_large_utf8(false),
        )
        .map_err(|e| format!("Failed to create fields from type: {}", e))?;

        let record_batch = serde_arrow::to_record_batch(&fields, &data)
            .map_err(|e| format!("Failed to create RecordBatch: {}", e))?;

        Ok(record_batch)
    }
}

impl Default for DimTestProgramTestItem {
    fn default() -> Self {
        Self {
            CUSTOMER: String::new(),
            SUB_CUSTOMER: String::new(),
            UPLOAD_TYPE: String::new(),
            FILE_ID: 0,
            FACTORY: String::new(),
            FACTORY_SITE: String::new(),
            FAB: String::new(),
            FAB_SITE: String::new(),
            TEST_AREA: String::new(),
            TEST_STAGE: String::new(),
            DEVICE_ID: String::new(),
            TEST_PROGRAM: String::new(),
            TEST_PROGRAM_VERSION: String::new(),
            TEST_TEMPERATURE: String::new(),
            TEST_NUM: None,
            TEST_NUM_KEY: String::new(),
            TEST_TXT: String::new(),
            TEST_ITEM: String::new(),
            TESTITEM_TYPE: String::new(),
            ORIGIN_HI_LIMIT: None,
            ORIGIN_HI_LIMIT_KEY: String::new(),
            ORIGIN_LO_LIMIT: None,
            ORIGIN_LO_LIMIT_KEY: String::new(),
            ORIGIN_UNITS: String::new(),
            LO_LIMIT: None,
            LO_LIMIT_KEY: String::new(),
            HI_LIMIT: None,
            HI_LIMIT_KEY: String::new(),
            UNITS: String::new(),
            CONDITION_SET: None,
            CONDITION_SET_STR: String::new(),
            CREATE_HOUR_KEY: String::new(),
            CREATE_DAY_KEY: String::new(),
            CREATE_TIME: 0,
            CREATE_USER: String::new(),
            UPLOAD_TIME: 0,
            VERSION: 0,
            IS_DELETE: 0,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    #[test]
    fn test_dim_test_program_test_item_default() {
        let item = DimTestProgramTestItem::default();
        assert_eq!(item.CUSTOMER, "");
        assert_eq!(item.FILE_ID, 0);
        assert_eq!(item.VERSION, 0);
        assert_eq!(item.IS_DELETE, 0);
    }

    #[test]
    fn test_dim_test_program_test_item_with_condition_set() {
        let mut condition_set = HashMap::new();
        condition_set.insert("frequency".to_string(), "1MHz".to_string());
        condition_set.insert("power".to_string(), "10dBm".to_string());

        let item = DimTestProgramTestItem {
            CUSTOMER: "TEST_CUSTOMER".to_string(),
            FILE_ID: 54321,
            TEST_PROGRAM: "TEST_PROG_V1".to_string(),
            CONDITION_SET: Some(condition_set),
            VERSION: 2,
            ..Default::default()
        };

        assert_eq!(item.CUSTOMER, "TEST_CUSTOMER");
        assert_eq!(item.FILE_ID, 54321);
        assert_eq!(item.TEST_PROGRAM, "TEST_PROG_V1");
        assert!(item.CONDITION_SET.is_some());
        assert_eq!(item.CONDITION_SET.as_ref().unwrap().len(), 2);
        assert_eq!(item.VERSION, 2);
    }

    #[test]
    fn test_record_batch_wrapper_empty_data() {
        let empty_data: Vec<DimTestProgramTestItem> = vec![];
        let result = DimTestProgramTestItem::to_record_batch(&empty_data);
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("Cannot create RecordBatch from empty data"));
    }
}
