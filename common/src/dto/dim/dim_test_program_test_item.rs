use crate::parquet::RecordBatchWrapper;
use arrow::array::RecordBatch;
use arrow::datatypes::{Field, FieldRef};
use serde::{Deserialize, Serialize};
use serde_arrow::{schema::TracingOptions, schema::Schema<PERSON><PERSON>};
use std::sync::Arc;


#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[allow(non_snake_case)]
pub struct DimTestProgramTestItem {
    // Customer and upload information
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub FILE_ID: u32,

    // Factory and location information
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,

    // Test stage information
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub DEVICE_ID: Arc<str>,

    // Test program information
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,

    // Test item information
    pub TEST_NUM: Option<u32>,
    pub TEST_NUM_KEY: Arc<str>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,
    pub TESTITEM_TYPE: Arc<str>,

    // Original limits
    pub ORIGIN_HI_LIMIT: Option<f64>,
    pub ORIGIN_HI_LIMIT_KEY: Arc<str>,
    pub ORIGIN_LO_LIMIT: Option<f64>,
    pub ORIGIN_LO_LIMIT_KEY: Arc<str>,
    pub ORIGIN_UNITS: Arc<str>,

    // Processed limits
    pub LO_LIMIT: Option<f64>,
    pub LO_LIMIT_KEY: Arc<str>,
    pub HI_LIMIT: Option<f64>,
    pub HI_LIMIT_KEY: Arc<str>,
    pub UNITS: Arc<str>,

    // Test conditions
    pub CONDITION_SET: Vec<(Arc<str>, Arc<str>)>,
    pub CONDITION_SET_STR: Arc<str>,

    // System fields
    pub CREATE_HOUR_KEY: Arc<str>,
    pub CREATE_DAY_KEY: Arc<str>,
    pub CREATE_TIME: i64, // Timestamp in milliseconds
    pub CREATE_USER: Arc<str>,
    pub UPLOAD_TIME: i64, // Timestamp in milliseconds
    pub VERSION: u32,
    pub IS_DELETE: u8,
}

impl RecordBatchWrapper for DimTestProgramTestItem {
    fn from_record_batch(batch: &RecordBatch) -> Result<Vec<Self>, String>
    where
        Self: Sized,
    {
        let result: Vec<DimTestProgramTestItem> = serde_arrow::from_record_batch(batch)
            .map_err(|e| format!("Failed to deserialize from RecordBatch: {}", e))?;
        Ok(result)
    }

    fn to_record_batch(data: &[Self]) -> Result<RecordBatch, String>
    where
        Self: Sized,
    {
        if data.is_empty() {
            return Err("Cannot create RecordBatch from empty data".to_string());
        }

        // Use DIM layer field processing logic
        let fields: Vec<Arc<Field>> = Vec::<FieldRef>::from_type::<DimTestProgramTestItem>(
            TracingOptions::default()
                .allow_null_fields(true)
                .map_as_struct(false)
                .strings_as_large_utf8(false),
        )
        .map_err(|e| format!("Failed to create fields from type: {}", e))?;

        let record_batch = serde_arrow::to_record_batch(&fields, &data)
            .map_err(|e| format!("Failed to create RecordBatch: {}", e))?;

        Ok(record_batch)
    }
}

impl Default for DimTestProgramTestItem {
    fn default() -> Self {
        use crate::model::constant::EMPTY;

        Self {
            CUSTOMER: Arc::from(EMPTY),
            SUB_CUSTOMER: Arc::from(EMPTY),
            UPLOAD_TYPE: Arc::from(EMPTY),
            FILE_ID: 0,
            FACTORY: Arc::from(EMPTY),
            FACTORY_SITE: Arc::from(EMPTY),
            FAB: Arc::from(EMPTY),
            FAB_SITE: Arc::from(EMPTY),
            TEST_AREA: Arc::from(EMPTY),
            TEST_STAGE: Arc::from(EMPTY),
            DEVICE_ID: Arc::from(EMPTY),
            TEST_PROGRAM: Arc::from(EMPTY),
            TEST_PROGRAM_VERSION: Arc::from(EMPTY),
            TEST_TEMPERATURE: Arc::from(EMPTY),
            TEST_NUM: None,
            TEST_NUM_KEY: Arc::from(EMPTY),
            TEST_TXT: Arc::from(EMPTY),
            TEST_ITEM: Arc::from(EMPTY),
            TESTITEM_TYPE: Arc::from(EMPTY),
            ORIGIN_HI_LIMIT: None,
            ORIGIN_HI_LIMIT_KEY: Arc::from(EMPTY),
            ORIGIN_LO_LIMIT: None,
            ORIGIN_LO_LIMIT_KEY: Arc::from(EMPTY),
            ORIGIN_UNITS: Arc::from(EMPTY),
            LO_LIMIT: None,
            LO_LIMIT_KEY: Arc::from(EMPTY),
            HI_LIMIT: None,
            HI_LIMIT_KEY: Arc::from(EMPTY),
            UNITS: Arc::from(EMPTY),
            CONDITION_SET: Vec::new(),
            CONDITION_SET_STR: Arc::from(EMPTY),
            CREATE_HOUR_KEY: Arc::from(EMPTY),
            CREATE_DAY_KEY: Arc::from(EMPTY),
            CREATE_TIME: 0,
            CREATE_USER: Arc::from(EMPTY),
            UPLOAD_TIME: 0,
            VERSION: 0,
            IS_DELETE: 0,
        }
    }
}


