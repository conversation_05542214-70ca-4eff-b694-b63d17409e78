use serde::{Deserialize, Serialize};
use std::sync::Arc;

/// TestProgramTestItem key for grouping and deduplication
/// Corresponds to TestProgramTestItemKey in Scala
/// Used for grouping TestProgramTestItem records and ensuring uniqueness
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
#[allow(non_snake_case)]
pub struct TestProgramTestItemKey {
    pub CUSTOMER: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,
    pub TEST_NUM_KEY: Arc<str>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,
    pub TESTITEM_TYPE: Arc<str>,
    pub ORIGIN_HI_LIMIT_KEY: Arc<str>,
    pub ORIGIN_LO_LIMIT_KEY: Arc<str>,
    pub ORIGIN_UNITS: Arc<str>,
    pub LO_LIMIT_KEY: Arc<str>,
    pub HI_LIMIT_KEY: Arc<str>,
    pub UNITS: Arc<str>,
    pub CONDITION_SET_STR: Arc<str>,
}

impl TestProgramTestItemKey {
    /// Creates a new TestProgramTestItemKey
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        customer: Arc<str>,
        test_stage: Arc<str>,
        test_program: Arc<str>,
        test_program_version: Arc<str>,
        test_temperature: Arc<str>,
        test_num_key: Arc<str>,
        test_txt: Arc<str>,
        test_item: Arc<str>,
        testitem_type: Arc<str>,
        origin_hi_limit_key: Arc<str>,
        origin_lo_limit_key: Arc<str>,
        origin_units: Arc<str>,
        lo_limit_key: Arc<str>,
        hi_limit_key: Arc<str>,
        units: Arc<str>,
        condition_set_str: Arc<str>,
    ) -> Self {
        Self {
            CUSTOMER: customer,
            TEST_STAGE: test_stage,
            TEST_PROGRAM: test_program,
            TEST_PROGRAM_VERSION: test_program_version,
            TEST_TEMPERATURE: test_temperature,
            TEST_NUM_KEY: test_num_key,
            TEST_TXT: test_txt,
            TEST_ITEM: test_item,
            TESTITEM_TYPE: testitem_type,
            ORIGIN_HI_LIMIT_KEY: origin_hi_limit_key,
            ORIGIN_LO_LIMIT_KEY: origin_lo_limit_key,
            ORIGIN_UNITS: origin_units,
            LO_LIMIT_KEY: lo_limit_key,
            HI_LIMIT_KEY: hi_limit_key,
            UNITS: units,
            CONDITION_SET_STR: condition_set_str,
        }
    }
}
