use serde::{Deserialize, Serialize};
use std::sync::Arc;
use crate::dto::dim::DimTestItemRow;

/// TestItem key for grouping and deduplication
/// Corresponds to <PERSON>ItemKey in Scala
/// Used for grouping TestItem records and ensuring uniqueness
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
#[allow(non_snake_case)]
pub struct TestItemKey {
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub FACTORY: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub TEST_ITEM: Arc<str>,
    pub LOT_ID: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub WAFER_NO_KEY: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub SITE: Option<u32>,
    pub SBIN_NUM: Option<u32>,
    pub HBIN_NUM: Option<u32>,
    pub TEST_STAGE: Arc<str>,
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,
    pub TESTER_NAME: Arc<str>,
    pub PROBER_HANDLER_ID: Arc<str>,
    pub PROBECARD_LOADBOARD_ID: Arc<str>,
    pub CONDITION_SET_STR: Arc<str>,
}

impl TestItemKey {
    /// Creates a new TestItemKey
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        customer: Arc<str>,
        sub_customer: Arc<str>,
        test_area: Arc<str>,
        factory: Arc<str>,
        device_id: Arc<str>,
        test_item: Arc<str>,
        lot_id: Arc<str>,
        lot_type: Arc<str>,
        wafer_no_key: Arc<str>,
        sblot_id: Arc<str>,
        site: Option<u32>,
        sbin_num: Option<u32>,
        hbin_num: Option<u32>,
        test_stage: Arc<str>,
        test_program: Arc<str>,
        test_temperature: Arc<str>,
        tester_name: Arc<str>,
        prober_handler_id: Arc<str>,
        probecard_loadboard_id: Arc<str>,
        condition_set_str: Arc<str>,
    ) -> Self {
        Self {
            CUSTOMER: customer,
            SUB_CUSTOMER: sub_customer,
            TEST_AREA: test_area,
            FACTORY: factory,
            DEVICE_ID: device_id,
            TEST_ITEM: test_item,
            LOT_ID: lot_id,
            LOT_TYPE: lot_type,
            WAFER_NO_KEY: wafer_no_key,
            SBLOT_ID: sblot_id,
            SITE: site,
            SBIN_NUM: sbin_num,
            HBIN_NUM: hbin_num,
            TEST_STAGE: test_stage,
            TEST_PROGRAM: test_program,
            TEST_TEMPERATURE: test_temperature,
            TESTER_NAME: tester_name,
            PROBER_HANDLER_ID: prober_handler_id,
            PROBECARD_LOADBOARD_ID: probecard_loadboard_id,
            CONDITION_SET_STR: condition_set_str,
        }
    }

    /// Creates a TestItemKey from DimTestItemRow
    /// Used for grouping DimTestItemRow records
    pub fn from_dim_test_item_row(row: &DimTestItemRow) -> Self {
        // Convert CONDITION_SET to string representation
        let condition_set_str = if row.CONDITION_SET.is_empty() {
            Arc::from("")
        } else {
            let condition_str = row
                .CONDITION_SET
                .iter()
                .map(|(k, v)| format!("{}={}", k, v))
                .collect::<Vec<_>>()
                .join(",");
            Arc::from(condition_str)
        };

        Self {
            CUSTOMER: row.CUSTOMER.clone(),
            SUB_CUSTOMER: row.SUB_CUSTOMER.clone(),
            TEST_AREA: row.TEST_AREA.clone(),
            FACTORY: row.FACTORY.clone(),
            DEVICE_ID: row.DEVICE_ID.clone(),
            TEST_ITEM: row.TEST_ITEM.clone(),
            LOT_ID: row.LOT_ID.clone(),
            LOT_TYPE: row.LOT_TYPE.clone(),
            WAFER_NO_KEY: row.WAFER_NO_KEY.clone(),
            SBLOT_ID: row.SBLOT_ID.clone(),
            SITE: row.SITE,
            SBIN_NUM: row.SBIN_NUM,
            HBIN_NUM: row.HBIN_NUM,
            TEST_STAGE: row.TEST_STAGE.clone(),
            TEST_PROGRAM: row.TEST_PROGRAM.clone(),
            TEST_TEMPERATURE: row.TEST_TEMPERATURE.clone(),
            TESTER_NAME: row.TESTER_NAME.clone(),
            PROBER_HANDLER_ID: row.PROBER_HANDLER_ID.clone(),
            PROBECARD_LOADBOARD_ID: row.PROBECARD_LOADBOARD_ID.clone(),
            CONDITION_SET_STR: condition_set_str,
        }
    }
}
