use crate::dto::dim::key::TestProgramTestItemKey;
use crate::dto::dim::{DimTestItemRow, DimTestProgramTestItemRow};
use crate::utils::decimal::Decimal38_18;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;

/// TestProgramTestItem intermediate structure
/// Corresponds to TestProgramTestItem case class in Scala
/// Contains all the information needed for DIM TestProgramTestItem processing
#[derive(Debug, Clone, Serialize, Deserialize)]
#[allow(non_snake_case)]
pub struct TestProgramTestItem {
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub FILE_ID: u32,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,
    pub TEST_NUM: Option<u32>,
    pub TEST_NUM_KEY: Arc<str>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,
    pub TESTITEM_TYPE: Arc<str>,
    pub ORIGIN_HI_LIMIT: Option<Decimal38_18>,
    pub ORIGIN_HI_LIMIT_KEY: Arc<str>,
    pub ORIGIN_LO_LIMIT: Option<Decimal38_18>,
    pub ORIGIN_LO_LIMIT_KEY: Arc<str>,
    pub ORIGIN_UNITS: Arc<str>,
    pub LO_LIMIT: Option<Decimal38_18>,
    pub LO_LIMIT_KEY: Arc<str>,
    pub HI_LIMIT: Option<Decimal38_18>,
    pub HI_LIMIT_KEY: Arc<str>,
    pub UNITS: Arc<str>,
    pub CONDITION_SET: HashMap<String, String>,
    pub CONDITION_SET_STR: Arc<str>,
    pub CREATE_HOUR_KEY: Arc<str>,
    pub CREATE_DAY_KEY: Arc<str>,
    pub CREATE_TIME: DateTime<Utc>,
    pub CREATE_USER: Arc<str>,
    pub VERSION: u32,
    pub UPLOAD_TIME: DateTime<Utc>,
}

impl TestProgramTestItem {
    pub fn to_row(&self) -> DimTestProgramTestItemRow {
        DimTestProgramTestItemRow::new(
            &self.CUSTOMER,
            &self.SUB_CUSTOMER,
            &self.UPLOAD_TYPE,
            self.FILE_ID,
            &self.FACTORY,
            &self.FACTORY_SITE,
            &self.FAB,
            &self.FAB_SITE,
            &self.TEST_AREA,
            &self.TEST_STAGE,
            &self.DEVICE_ID,
            &self.TEST_PROGRAM,
            &self.TEST_PROGRAM_VERSION,
            &self.TEST_TEMPERATURE,
            self.TEST_NUM,
            &self.TEST_TXT,
            &self.TEST_ITEM,
            &self.TESTITEM_TYPE,
            self.ORIGIN_HI_LIMIT,
            self.ORIGIN_LO_LIMIT,
            &self.ORIGIN_UNITS,
            self.LO_LIMIT,
            self.HI_LIMIT,
            &self.UNITS,
            &self.CONDITION_SET,
            self.CREATE_TIME,
            &self.CREATE_USER,
            self.UPLOAD_TIME,
            self.VERSION,
        )
    }

    pub fn from_dim_test_item(test_item: &DimTestItemRow) -> Self {
        // Convert condition set to sorted string
        let condition_set_str = {
            let mut pairs: Vec<(String, String)> = test_item
                .CONDITION_SET
                .iter()
                .map(|(k, v)| (k.as_ref().to_string(), v.as_ref().to_string()))
                .collect();
            pairs.sort_by(|a, b| a.0.cmp(&b.0));
            pairs.iter().map(|(k, v)| format!("{}:{}", k, v)).collect::<Vec<_>>().join(",")
        };

        // Convert condition set to regular HashMap<String, String>
        let condition_set: HashMap<String, String> = test_item
            .CONDITION_SET
            .iter()
            .map(|(k, v)| (k.as_ref().to_string(), v.as_ref().to_string()))
            .collect();

        Self {
            CUSTOMER: test_item.CUSTOMER.clone(),
            SUB_CUSTOMER: test_item.SUB_CUSTOMER.clone(),
            UPLOAD_TYPE: test_item.UPLOAD_TYPE.clone(),
            FILE_ID: test_item.FILE_ID,
            FACTORY: test_item.FACTORY.clone(),
            FACTORY_SITE: test_item.FACTORY_SITE.clone(),
            FAB: test_item.FAB.clone(),
            FAB_SITE: test_item.FAB_SITE.clone(),
            TEST_AREA: test_item.TEST_AREA.clone(),
            TEST_STAGE: test_item.TEST_STAGE.clone(),
            DEVICE_ID: test_item.DEVICE_ID.clone(),
            TEST_PROGRAM: test_item.TEST_PROGRAM.clone(),
            TEST_PROGRAM_VERSION: test_item.TEST_PROGRAM_VERSION.clone(),
            TEST_TEMPERATURE: test_item.TEST_TEMPERATURE.clone(),
            TEST_NUM: test_item.TEST_NUM,
            TEST_NUM_KEY: test_item.TEST_NUM.map_or_else(|| "".into(), |v| v.to_string().into()),
            TEST_TXT: test_item.TEST_TXT.clone(),
            TEST_ITEM: test_item.TEST_ITEM.clone(),
            TESTITEM_TYPE: test_item.TESTITEM_TYPE.clone(),
            ORIGIN_HI_LIMIT: test_item.ORIGIN_HI_LIMIT,
            ORIGIN_HI_LIMIT_KEY: test_item.ORIGIN_HI_LIMIT.map_or_else(|| "".into(), |v| v.to_string().into()),
            ORIGIN_LO_LIMIT: test_item.ORIGIN_LO_LIMIT,
            ORIGIN_LO_LIMIT_KEY: test_item.ORIGIN_LO_LIMIT.map_or_else(|| "".into(), |v| v.to_string().into()),
            ORIGIN_UNITS: test_item.ORIGIN_UNITS.clone(),
            LO_LIMIT: test_item.LO_LIMIT,
            LO_LIMIT_KEY: test_item.LO_LIMIT.map_or_else(|| "".into(), |v| v.to_string().into()),
            HI_LIMIT: test_item.HI_LIMIT,
            HI_LIMIT_KEY: test_item.HI_LIMIT.map_or_else(|| "".into(), |v| v.to_string().into()),
            UNITS: test_item.UNITS.clone(),
            CONDITION_SET: condition_set,
            CONDITION_SET_STR: condition_set_str.into(),
            CREATE_HOUR_KEY: test_item.CREATE_HOUR_KEY.clone(),
            CREATE_DAY_KEY: test_item.CREATE_DAY_KEY.clone(),
            CREATE_TIME: test_item.CREATE_TIME,
            CREATE_USER: test_item.CREATE_USER.clone(),
            VERSION: test_item.VERSION as u32,
            UPLOAD_TIME: test_item.UPLOAD_TIME,
        }
    }

    /// Creates a TestProgramTestItemKey from this TestProgramTestItem
    pub fn to_key(&self) -> TestProgramTestItemKey {
        TestProgramTestItemKey::new(
            self.CUSTOMER.clone(),
            self.TEST_STAGE.clone(),
            self.TEST_PROGRAM.clone(),
            self.TEST_PROGRAM_VERSION.clone(),
            self.TEST_TEMPERATURE.clone(),
            self.TEST_NUM_KEY.clone(),
            self.TEST_TXT.clone(),
            self.TEST_ITEM.clone(),
            self.TESTITEM_TYPE.clone(),
            self.ORIGIN_HI_LIMIT_KEY.clone(),
            self.ORIGIN_LO_LIMIT_KEY.clone(),
            self.ORIGIN_UNITS.clone(),
            self.LO_LIMIT_KEY.clone(),
            self.HI_LIMIT_KEY.clone(),
            self.UNITS.clone(),
            self.CONDITION_SET_STR.clone(),
        )
    }
}
