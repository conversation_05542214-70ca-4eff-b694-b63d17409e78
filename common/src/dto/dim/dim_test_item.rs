use crate::parquet::RecordBatchWrapper;
use arrow::array::RecordBatch;
use arrow::datatypes::{Field, FieldRef};
use serde::{Deserialize, Serialize};
use serde_arrow::{schema::TracingOptions, schema::SchemaLike};
use std::sync::Arc;

/// DIM TestItem structure for HDFS/Parquet storage
///
/// This structure uses Arc<str> types for memory efficiency and consistency
/// with other DIM structures. It corresponds to DimTestItemRow and implements
/// RecordBatchWrapper for Parquet operations.
///
/// # Usage
/// ```rust
/// // Use RecordBatchWrapper for Parquet operations
/// let record_batch = DimTestItem::to_record_batch(&data)?;
/// let data = DimTestItem::from_record_batch(&record_batch)?;
/// ```
#[derive(Debug, Clone, Serialize, Deserialize)]
#[allow(non_snake_case)]
pub struct DimTestItem {
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub FILE_ID: u32,
    pub FILE_NAME: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub LOT_ID: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub PROCESS: Arc<str>,
    pub WAFER_ID: Arc<str>,
    pub WAFER_ID_KEY: Arc<str>,
    pub WAFER_NO: Arc<str>,
    pub WAFER_NO_KEY: Arc<str>,
    pub WAFER_LOT_ID: Arc<str>,
    pub FABWF_ID: Arc<str>,
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,
    pub TESTER_NAME: Arc<str>,
    pub TESTER_TYPE: Arc<str>,
    pub OPERATOR_NAME: Arc<str>,
    pub PROBER_HANDLER_TYP: Arc<str>,
    pub PROBER_HANDLER_ID: Arc<str>,
    pub PROBECARD_LOADBOARD_TYP: Arc<str>,
    pub PROBECARD_LOADBOARD_ID: Arc<str>,
    pub START_TIME: Option<i64>, // Timestamp in milliseconds
    pub END_TIME: Option<i64>,   // Timestamp in milliseconds
    pub START_HOUR_KEY: Arc<str>,
    pub START_DAY_KEY: Arc<str>,
    pub END_HOUR_KEY: Arc<str>,
    pub END_DAY_KEY: Arc<str>,
    pub TEST_HEAD: Option<u32>,
    pub SITE: Option<u32>,
    pub SITE_KEY: Arc<str>,
    pub HBIN_NUM: Option<u32>,
    pub HBIN_NUM_KEY: Arc<str>,
    pub SBIN_NUM: Option<u32>,
    pub SBIN_NUM_KEY: Arc<str>,
    pub SBIN_PF: Arc<str>,
    pub SBIN_NAM: Arc<str>,
    pub HBIN_PF: Arc<str>,
    pub HBIN_NAM: Arc<str>,
    pub HBIN: Arc<str>,
    pub SBIN: Arc<str>,
    pub TEST_NUM: Option<u32>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,
    pub TESTITEM_TYPE: Arc<str>,
    pub ORIGIN_HI_LIMIT: Option<f64>,
    pub ORIGIN_LO_LIMIT: Option<f64>,
    pub ORIGIN_UNITS: Arc<str>,
    pub LO_LIMIT: Option<f64>,
    pub HI_LIMIT: Option<f64>,
    pub UNITS: Arc<str>,
    pub CONDITION_SET: Vec<(Arc<str>, Arc<str>)>,
    pub CREATE_HOUR_KEY: Arc<str>,
    pub CREATE_DAY_KEY: Arc<str>,
    pub CREATE_TIME: i64, // Timestamp in milliseconds
    pub CREATE_USER: Arc<str>,
    pub UPLOAD_TIME: i64, // Timestamp in milliseconds
    pub VERSION: i64,
    pub IS_DELETE: u8,
}

impl RecordBatchWrapper for DimTestItem {
    fn from_record_batch(batch: &RecordBatch) -> Result<Vec<Self>, String>
    where
        Self: Sized,
    {
        let result: Vec<DimTestItem> = serde_arrow::from_record_batch(batch)
            .map_err(|e| format!("Failed to deserialize from RecordBatch: {}", e))?;
        Ok(result)
    }

    fn to_record_batch(data: &[Self]) -> Result<RecordBatch, String>
    where
        Self: Sized,
    {
        if data.is_empty() {
            return Err("Cannot create RecordBatch from empty data".to_string());
        }

        // Use DIM layer field processing logic
        let fields: Vec<Arc<Field>> = Vec::<FieldRef>::from_type::<DimTestItem>(
            TracingOptions::default()
                .allow_null_fields(true)
                .map_as_struct(false)
                .strings_as_large_utf8(false),
        )
        .map_err(|e| format!("Failed to create fields from type: {}", e))?;

        let record_batch = serde_arrow::to_record_batch(&fields, &data)
            .map_err(|e| format!("Failed to create RecordBatch: {}", e))?;

        Ok(record_batch)
    }
}

impl Default for DimTestItem {
    fn default() -> Self {
        use crate::model::constant::EMPTY;

        Self {
            CUSTOMER: Arc::from(EMPTY),
            SUB_CUSTOMER: Arc::from(EMPTY),
            UPLOAD_TYPE: Arc::from(EMPTY),
            FILE_ID: 0,
            FILE_NAME: Arc::from(EMPTY),
            FACTORY: Arc::from(EMPTY),
            FACTORY_SITE: Arc::from(EMPTY),
            FAB: Arc::from(EMPTY),
            FAB_SITE: Arc::from(EMPTY),
            TEST_AREA: Arc::from(EMPTY),
            TEST_STAGE: Arc::from(EMPTY),
            LOT_TYPE: Arc::from(EMPTY),
            DEVICE_ID: Arc::from(EMPTY),
            LOT_ID: Arc::from(EMPTY),
            SBLOT_ID: Arc::from(EMPTY),
            PROCESS: Arc::from(EMPTY),
            WAFER_ID: Arc::from(EMPTY),
            WAFER_ID_KEY: Arc::from(EMPTY),
            WAFER_NO: Arc::from(EMPTY),
            WAFER_NO_KEY: Arc::from(EMPTY),
            WAFER_LOT_ID: Arc::from(EMPTY),
            FABWF_ID: Arc::from(EMPTY),
            TEST_PROGRAM: Arc::from(EMPTY),
            TEST_PROGRAM_VERSION: Arc::from(EMPTY),
            TEST_TEMPERATURE: Arc::from(EMPTY),
            TESTER_NAME: Arc::from(EMPTY),
            TESTER_TYPE: Arc::from(EMPTY),
            OPERATOR_NAME: Arc::from(EMPTY),
            PROBER_HANDLER_TYP: Arc::from(EMPTY),
            PROBER_HANDLER_ID: Arc::from(EMPTY),
            PROBECARD_LOADBOARD_TYP: Arc::from(EMPTY),
            PROBECARD_LOADBOARD_ID: Arc::from(EMPTY),
            START_TIME: None,
            END_TIME: None,
            START_HOUR_KEY: Arc::from(EMPTY),
            START_DAY_KEY: Arc::from(EMPTY),
            END_HOUR_KEY: Arc::from(EMPTY),
            END_DAY_KEY: Arc::from(EMPTY),
            TEST_HEAD: None,
            SITE: None,
            SITE_KEY: Arc::from(EMPTY),
            HBIN_NUM: None,
            HBIN_NUM_KEY: Arc::from(EMPTY),
            SBIN_NUM: None,
            SBIN_NUM_KEY: Arc::from(EMPTY),
            SBIN_PF: Arc::from(EMPTY),
            SBIN_NAM: Arc::from(EMPTY),
            HBIN_PF: Arc::from(EMPTY),
            HBIN_NAM: Arc::from(EMPTY),
            HBIN: Arc::from(EMPTY),
            SBIN: Arc::from(EMPTY),
            TEST_NUM: None,
            TEST_TXT: Arc::from(EMPTY),
            TEST_ITEM: Arc::from(EMPTY),
            TESTITEM_TYPE: Arc::from(EMPTY),
            ORIGIN_HI_LIMIT: None,
            ORIGIN_LO_LIMIT: None,
            ORIGIN_UNITS: Arc::from(EMPTY),
            LO_LIMIT: None,
            HI_LIMIT: None,
            UNITS: Arc::from(EMPTY),
            CONDITION_SET: Vec::new(),
            CREATE_HOUR_KEY: Arc::from(EMPTY),
            CREATE_DAY_KEY: Arc::from(EMPTY),
            CREATE_TIME: 0,
            CREATE_USER: Arc::from(EMPTY),
            UPLOAD_TIME: 0,
            VERSION: 0,
            IS_DELETE: 0,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_dim_test_item_default() {
        let item = DimTestItem::default();
        assert_eq!(item.CUSTOMER.as_ref(), "");
        assert_eq!(item.FILE_ID, 0);
        assert_eq!(item.VERSION, 0);
        assert_eq!(item.IS_DELETE, 0);
    }

    #[test]
    fn test_dim_test_item_with_condition_set() {
        let condition_set = vec![
            (Arc::from("temp"), Arc::from("25C")),
            (Arc::from("voltage"), Arc::from("3.3V")),
        ];

        let item = DimTestItem {
            CUSTOMER: Arc::from("TEST_CUSTOMER"),
            FILE_ID: 12345,
            CONDITION_SET: condition_set,
            VERSION: 1,
            ..Default::default()
        };

        assert_eq!(item.CUSTOMER.as_ref(), "TEST_CUSTOMER");
        assert_eq!(item.FILE_ID, 12345);
        assert_eq!(item.CONDITION_SET.len(), 2);
        assert_eq!(item.VERSION, 1);
    }

    #[test]
    fn test_record_batch_wrapper_empty_data() {
        let empty_data: Vec<DimTestItem> = vec![];
        let result = DimTestItem::to_record_batch(&empty_data);
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("Cannot create RecordBatch from empty data"));
    }
}
