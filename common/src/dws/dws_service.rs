//! DWS服务基础实现
//!
//! 对应Scala中的DwsService类

use crate::dto::dwd::die_detail_row::DieDetailRow;
use crate::dto::dwd::die_detail_parquet::DieDetailParquet;
use crate::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use std::sync::Arc;

/// DWS服务基础类
/// 对应Scala中的com.guwave.onedata.dataware.dw.common.dws.service.DwsService
pub struct DwsService;

impl DwsService {
    /// 构建DWS子Die详情 - 从DieDetailParquet
    /// 对应Scala中的buildDwsSubDieDetail方法，处理parquet格式的数据
    pub fn build_dws_sub_die_detail_from_parquet(die_detail: &DieDetailParquet) -> DwsSubDieDetail {
        DwsSubDieDetail {
            CUSTOMER: Arc::from(die_detail.CUSTOMER.as_deref().unwrap_or("")),
            SUB_CUSTOMER: Arc::from(die_detail.SUB_CUSTOMER.as_deref().unwrap_or("")),
            UPLOAD_TYPE: Arc::from(die_detail.UPLOAD_TYPE.as_deref().unwrap_or("")),
            FILE_ID: die_detail.FILE_ID,
            FILE_NAME: Arc::from(die_detail.FILE_NAME.as_deref().unwrap_or("")),
            FILE_TYPE: Arc::from(die_detail.FILE_TYPE.as_deref().unwrap_or("")),
            FACTORY: Arc::from(die_detail.FACTORY.as_deref().unwrap_or("")),
            FACTORY_SITE: Arc::from(die_detail.FACTORY_SITE.as_deref().unwrap_or("")),
            FAB: Arc::from(die_detail.FAB.as_deref().unwrap_or("")),
            FAB_SITE: Arc::from(die_detail.FAB_SITE.as_deref().unwrap_or("")),
            LOT_TYPE: Arc::from(die_detail.LOT_TYPE.as_deref().unwrap_or("")),
            TEST_AREA: Arc::from(die_detail.TEST_AREA.as_deref().unwrap_or("")),
            ECID: Arc::from(die_detail.ECID.as_deref().unwrap_or("")),
            IS_STANDARD_ECID: die_detail.IS_STANDARD_ECID,
            SITE: die_detail.SITE,
            OFFLINE_RETEST: die_detail.OFFLINE_RETEST,
            OFFLINE_RETEST_IGNORE_TP: die_detail.OFFLINE_RETEST_IGNORE_TP,
            ONLINE_RETEST: die_detail.ONLINE_RETEST,
            INTERRUPT: die_detail.INTERRUPT,
            INTERRUPT_IGNORE_TP: die_detail.INTERRUPT_IGNORE_TP,
            DUP_RETEST: die_detail.DUP_RETEST,
            DUP_RETEST_IGNORE_TP: die_detail.DUP_RETEST_IGNORE_TP,
            BATCH_NUM: die_detail.BATCH_NUM,
            BATCH_NUM_IGNORE_TP: die_detail.BATCH_NUM_IGNORE_TP,
            IS_FIRST_TEST: die_detail.IS_FIRST_TEST,
            IS_FINAL_TEST: die_detail.IS_FINAL_TEST,
            IS_FIRST_TEST_IGNORE_TP: die_detail.IS_FIRST_TEST_IGNORE_TP,
            IS_FINAL_TEST_IGNORE_TP: die_detail.IS_FINAL_TEST_IGNORE_TP,
            IS_DUP_FIRST_TEST: die_detail.IS_DUP_FIRST_TEST,
            IS_DUP_FINAL_TEST: die_detail.IS_DUP_FINAL_TEST,
            IS_DUP_FIRST_TEST_IGNORE_TP: die_detail.IS_DUP_FIRST_TEST_IGNORE_TP,
            IS_DUP_FINAL_TEST_IGNORE_TP: die_detail.IS_DUP_FINAL_TEST_IGNORE_TP,
            WAFER_ID: Arc::from(die_detail.WAFER_ID.as_deref().unwrap_or("")),
            WAFER_NO: Arc::from(die_detail.WAFER_NO.as_deref().unwrap_or("")),
            LOT_ID: Arc::from(die_detail.LOT_ID.as_deref().unwrap_or("")),
            SBLOT_ID: Arc::from(die_detail.SBLOT_ID.as_deref().unwrap_or("")),
            WAFER_LOT_ID: Arc::from(die_detail.WAFER_LOT_ID.as_deref().unwrap_or("")),
            PROBER_HANDLER_ID: Arc::from(die_detail.PROBER_HANDLER_ID.as_deref().unwrap_or("")),
            TESTER_TYPE: Arc::from(die_detail.TESTER_TYPE.as_deref().unwrap_or("")),
            OPERATOR_NAME: Arc::from(die_detail.OPERATOR_NAME.as_deref().unwrap_or("")),
            TEST_STAGE: Arc::from(die_detail.TEST_STAGE.as_deref().unwrap_or("")),
            DEVICE_ID: Arc::from(die_detail.DEVICE_ID.as_deref().unwrap_or("")),
            TEST_PROGRAM: Arc::from(die_detail.TEST_PROGRAM.as_deref().unwrap_or("")),
            TEST_TEMPERATURE: Arc::from(die_detail.TEST_TEMPERATURE.as_deref().unwrap_or("")),
            TEST_PROGRAM_VERSION: Arc::from(die_detail.TEST_PROGRAM_VERSION.as_deref().unwrap_or("")),
            TESTER_NAME: Arc::from(die_detail.TESTER_NAME.as_deref().unwrap_or("")),
            PROBECARD_LOADBOARD_ID: Arc::from(die_detail.PROBECARD_LOADBOARD_ID.as_deref().unwrap_or("")),
            FLOW_ID: Arc::from(die_detail.FLOW_ID.as_deref().unwrap_or("")),
            FLOW_ID_IGNORE_TP: Arc::from(die_detail.FLOW_ID_IGNORE_TP.as_deref().unwrap_or("")),
            DIE_CNT: die_detail.DIE_CNT,
            X_COORD: die_detail.X_COORD,
            Y_COORD: die_detail.Y_COORD,
            DIE_X: die_detail.DIE_X,
            DIE_Y: die_detail.DIE_Y,
            WF_FLAT: Arc::from(die_detail.WF_FLAT.as_deref().unwrap_or("")),
            FABWF_ID: Arc::from(die_detail.FABWF_ID.as_deref().unwrap_or("")),
            POS_X: Arc::from(die_detail.POS_X.as_deref().unwrap_or("")),
            POS_Y: Arc::from(die_detail.POS_Y.as_deref().unwrap_or("")),
            RETICLE_X: die_detail.RETICLE_X,
            RETICLE_Y: die_detail.RETICLE_Y,
            SITE_ID: Arc::from(die_detail.SITE_ID.as_deref().unwrap_or("")),
            TEST_TIME: die_detail.TEST_TIME,
            PART_ID: Arc::from(die_detail.PART_ID.as_deref().unwrap_or("")),
            SBIN_NUM: die_detail.SBIN_NUM,
            SBIN_PF: Arc::from(die_detail.SBIN_PF.as_deref().unwrap_or("")),
            SBIN_NAM: Arc::from(die_detail.SBIN_NAM.as_deref().unwrap_or("")),
            HBIN_NUM: die_detail.HBIN_NUM,
            HBIN_PF: Arc::from(die_detail.HBIN_PF.as_deref().unwrap_or("")),
            HBIN_NAM: Arc::from(die_detail.HBIN_NAM.as_deref().unwrap_or("")),
            HBIN: Arc::from(die_detail.HBIN.as_deref().unwrap_or("")),
            SBIN: Arc::from(die_detail.SBIN.as_deref().unwrap_or("")),
            START_TIME: die_detail.START_TIME,
            END_TIME: die_detail.END_TIME,
            START_HOUR_KEY: Arc::from(die_detail.START_HOUR_KEY.as_deref().unwrap_or("")),
            START_DAY_KEY: Arc::from(die_detail.START_DAY_KEY.as_deref().unwrap_or("")),
            END_HOUR_KEY: Arc::from(die_detail.END_HOUR_KEY.as_deref().unwrap_or("")),
            END_DAY_KEY: Arc::from(die_detail.END_DAY_KEY.as_deref().unwrap_or("")),
            C_PART_ID: die_detail.C_PART_ID,
            RETEST_BIN_NUM: Arc::from(die_detail.RETEST_BIN_NUM.as_deref().unwrap_or("")),
            PROCESS: Arc::from(die_detail.PROCESS.as_deref().unwrap_or("")),
            CREATE_TIME: die_detail.CREATE_TIME,
            UPLOAD_TIME: die_detail.UPLOAD_TIME,
            TEST_HEAD: die_detail.TEST_HEAD,
            PROBER_HANDLER_TYP: Arc::from(die_detail.PROBER_HANDLER_TYP.as_deref().unwrap_or("")),
            PROBECARD_LOADBOARD_TYP: Arc::from(die_detail.PROBECARD_LOADBOARD_TYP.as_deref().unwrap_or("")),
        }
    }

    /// 构建DWS子测试项详情并填充文件信息
    /// 整合了buildDwsSubTestItemDetail和fillFileInfo的功能
    pub fn build_dws_sub_test_item_detail_with_file_info(
        test_item_detail: &SubTestItemDetail,
        file_detail_map: &std::collections::HashMap<i64, DwsSubFileDetail>,
    ) -> DwsSubTestItemDetail {
        let mut dws_item = Self::build_dws_sub_test_item_detail(test_item_detail);
        
        // 填充文件信息
        if let Some(file_id) = test_item_detail.FILE_ID {
            if let Some(file_detail) = file_detail_map.get(&file_id) {
                Self::fill_file_info(&mut dws_item, file_detail);
            }
        }
        
        dws_item
    }

    /// 构建DWS子测试项详情
    /// 对应Scala中的buildDwsSubTestItemDetail方法
    pub fn build_dws_sub_test_item_detail(test_item_detail: &SubTestItemDetail) -> DwsSubTestItemDetail {
        DwsSubTestItemDetail {
            FILE_ID: test_item_detail.FILE_ID,
            ONLINE_RETEST: test_item_detail.ONLINE_RETEST,
            MAX_OFFLINE_RETEST: test_item_detail.MAX_OFFLINE_RETEST,
            MAX_ONLINE_RETEST: test_item_detail.MAX_ONLINE_RETEST,
            IS_DIE_FIRST_TEST: test_item_detail.IS_DIE_FIRST_TEST,
            IS_DIE_FINAL_TEST: test_item_detail.IS_DIE_FINAL_TEST,
            IS_FIRST_TEST: test_item_detail.IS_FIRST_TEST,
            IS_FINAL_TEST: test_item_detail.IS_FINAL_TEST,
            IS_FIRST_TEST_IGNORE_TP: test_item_detail.IS_FIRST_TEST_IGNORE_TP,
            IS_FINAL_TEST_IGNORE_TP: test_item_detail.IS_FINAL_TEST_IGNORE_TP,
            IS_DUP_FIRST_TEST: test_item_detail.IS_DUP_FIRST_TEST,
            IS_DUP_FINAL_TEST: test_item_detail.IS_DUP_FINAL_TEST,
            IS_DUP_FIRST_TEST_IGNORE_TP: test_item_detail.IS_DUP_FIRST_TEST_IGNORE_TP,
            IS_DUP_FINAL_TEST_IGNORE_TP: test_item_detail.IS_DUP_FINAL_TEST_IGNORE_TP,
            TEST_SUITE: Arc::from(test_item_detail.TEST_SUITE.as_deref().unwrap_or("")),
            CONDITION_SET: test_item_detail.CONDITION_SET.clone(),
            TEST_NUM: test_item_detail.TEST_NUM,
            TEST_TXT: Arc::from(test_item_detail.TEST_TXT.as_deref().unwrap_or("")),
            TEST_ITEM: Arc::from(test_item_detail.TEST_ITEM.as_deref().unwrap_or("")),
            IS_DIE_FIRST_TEST_ITEM: test_item_detail.IS_DIE_FIRST_TEST_ITEM,
            TESTITEM_TYPE: Arc::from(test_item_detail.TESTITEM_TYPE.as_deref().unwrap_or("")),
            TEST_FLG: Arc::from(test_item_detail.TEST_FLG.as_deref().unwrap_or("")),
            PARM_FLG: Arc::from(test_item_detail.PARM_FLG.as_deref().unwrap_or("")),
            TEST_STATE: Arc::from(test_item_detail.TEST_STATE.as_deref().unwrap_or("")),
            TEST_VALUE: test_item_detail.TEST_VALUE,
            UNITS: Arc::from(test_item_detail.UNITS.as_deref().unwrap_or("")),
            TEST_RESULT: test_item_detail.TEST_RESULT,
            ORIGIN_TEST_VALUE: test_item_detail.ORIGIN_TEST_VALUE,
            ORIGIN_UNITS: Arc::from(test_item_detail.ORIGIN_UNITS.as_deref().unwrap_or("")),
            TEST_ORDER: test_item_detail.TEST_ORDER,
            ALARM_ID: Arc::from(test_item_detail.ALARM_ID.as_deref().unwrap_or("")),
            OPT_FLG: Arc::from(test_item_detail.OPT_FLG.as_deref().unwrap_or("")),
            RES_SCAL: test_item_detail.RES_SCAL,
            NUM_TEST: test_item_detail.NUM_TEST,
            LLM_SCAL: test_item_detail.LLM_SCAL,
            HLM_SCAL: test_item_detail.HLM_SCAL,
            LO_LIMIT: test_item_detail.LO_LIMIT,
            HI_LIMIT: test_item_detail.HI_LIMIT,
            ORIGIN_HI_LIMIT: test_item_detail.ORIGIN_HI_LIMIT,
            ORIGIN_LO_LIMIT: test_item_detail.ORIGIN_LO_LIMIT,
            C_RESFMT: Arc::from(test_item_detail.C_RESFMT.as_deref().unwrap_or("")),
            C_LLMFMT: Arc::from(test_item_detail.C_LLMFMT.as_deref().unwrap_or("")),
            C_HLMFMT: Arc::from(test_item_detail.C_HLMFMT.as_deref().unwrap_or("")),
            LO_SPEC: test_item_detail.LO_SPEC,
            HI_SPEC: test_item_detail.HI_SPEC,
            HBIN_NUM: test_item_detail.HBIN_NUM,
            SBIN_NUM: test_item_detail.SBIN_NUM,
            SBIN_PF: Arc::from(test_item_detail.SBIN_PF.as_deref().unwrap_or("")),
            SBIN_NAM: Arc::from(test_item_detail.SBIN_NAM.as_deref().unwrap_or("")),
            HBIN_PF: Arc::from(test_item_detail.HBIN_PF.as_deref().unwrap_or("")),
            HBIN_NAM: Arc::from(test_item_detail.HBIN_NAM.as_deref().unwrap_or("")),
            HBIN: Arc::from(test_item_detail.HBIN.as_deref().unwrap_or("")),
            SBIN: Arc::from(test_item_detail.SBIN.as_deref().unwrap_or("")),
            TEST_HEAD: test_item_detail.TEST_HEAD,
            PART_FLG: Arc::from(test_item_detail.PART_FLG.as_deref().unwrap_or("")),
            PART_ID: Arc::from(test_item_detail.PART_ID.as_deref().unwrap_or("")),
            C_PART_ID: test_item_detail.C_PART_ID,
            ECID: Arc::from(test_item_detail.ECID.as_deref().unwrap_or("")),
            ECID_EXT: Arc::from(test_item_detail.ECID_EXT.as_deref().unwrap_or("")),
            ECID_EXTRA: test_item_detail.ECID_EXTRA.clone(),
            IS_STANDARD_ECID: test_item_detail.IS_STANDARD_ECID,
            X_COORD: test_item_detail.X_COORD,
            Y_COORD: test_item_detail.Y_COORD,
            DIE_X: test_item_detail.DIE_X,
            DIE_Y: test_item_detail.DIE_Y,
            TEST_TIME: test_item_detail.TEST_TIME,
            PART_TXT: Arc::from(test_item_detail.PART_TXT.as_deref().unwrap_or("")),
            PART_FIX: Arc::from(test_item_detail.PART_FIX.as_deref().unwrap_or("")),
            SITE: test_item_detail.SITE,
            TOUCH_DOWN_ID: test_item_detail.TOUCH_DOWN_ID,
            WAFER_LOT_ID: Arc::from(test_item_detail.WAFER_LOT_ID.as_deref().unwrap_or("")),
            WAFER_ID: Arc::from(test_item_detail.WAFER_ID.as_deref().unwrap_or("")),
            WAFER_NO: Arc::from(test_item_detail.WAFER_NO.as_deref().unwrap_or("")),
            RETICLE_T_X: test_item_detail.RETICLE_T_X,
            RETICLE_T_Y: test_item_detail.RETICLE_T_Y,
            RETICLE_X: test_item_detail.RETICLE_X,
            RETICLE_Y: test_item_detail.RETICLE_Y,
            SITE_ID: Arc::from(test_item_detail.SITE_ID.as_deref().unwrap_or("")),
            VECT_NAM: Arc::from(test_item_detail.VECT_NAM.as_deref().unwrap_or("")),
            TIME_SET: Arc::from(test_item_detail.TIME_SET.as_deref().unwrap_or("")),
            NUM_FAIL: test_item_detail.NUM_FAIL,
            FAIL_PIN: Arc::from(test_item_detail.FAIL_PIN.as_deref().unwrap_or("")),
            CYCL_CNT: test_item_detail.CYCL_CNT,
            REPT_CNT: test_item_detail.REPT_CNT,
            LONG_ATTRIBUTE_SET: test_item_detail.LONG_ATTRIBUTE_SET.clone(),
            STRING_ATTRIBUTE_SET: test_item_detail.STRING_ATTRIBUTE_SET.clone(),
            FLOAT_ATTRIBUTE_SET: test_item_detail.FLOAT_ATTRIBUTE_SET.clone(),
            UID: Arc::from(test_item_detail.UID.as_deref().unwrap_or("")),
            TEXT_DAT: Arc::from(test_item_detail.TEXT_DAT.as_deref().unwrap_or("")),
            CREATE_HOUR_KEY: Arc::from(test_item_detail.CREATE_HOUR_KEY.as_deref().unwrap_or("")),
            CREATE_DAY_KEY: Arc::from(test_item_detail.CREATE_DAY_KEY.as_deref().unwrap_or("")),
            CREATE_TIME: Some(test_item_detail.CREATE_TIME),
            EFUSE_EXTRA: test_item_detail.EFUSE_EXTRA.clone(),
            CHIP_ID: Arc::from(test_item_detail.CHIP_ID.as_deref().unwrap_or("")),
            CUSTOMER: Arc::from(""), // 将在fill_file_info中填充
            SUB_CUSTOMER: Arc::from(""),
            UPLOAD_TYPE: Arc::from(""),
            FILE_NAME: Arc::from(""),
            FILE_TYPE: Arc::from(""),
            FACTORY: Arc::from(""),
            FACTORY_SITE: Arc::from(""),
            FAB: Arc::from(""),
            FAB_SITE: Arc::from(""),
            LOT_TYPE: Arc::from(""),
            TEST_AREA: Arc::from(""),
            OFFLINE_RETEST: None,
            INTERRUPT: None,
            DUP_RETEST: None,
            BATCH_NUM: None,
            LOT_ID: Arc::from(""),
            SBLOT_ID: Arc::from(""),
            PROBER_HANDLER_ID: Arc::from(""),
            TESTER_TYPE: Arc::from(""),
            TEST_STAGE: Arc::from(""),
            DEVICE_ID: Arc::from(""),
            TEST_PROGRAM: Arc::from(""),
            TEST_TEMPERATURE: Arc::from(""),
            TEST_PROGRAM_VERSION: Arc::from(""),
            TESTER_NAME: Arc::from(""),
            OPERATOR_NAME: Arc::from(""),
            PROBECARD_LOADBOARD_ID: Arc::from(""),
            START_TIME: None,
            END_TIME: None,
            START_HOUR_KEY: Arc::from(""),
            START_DAY_KEY: Arc::from(""),
            END_HOUR_KEY: Arc::from(""),
            END_DAY_KEY: Arc::from(""),
            FLOW_ID: Arc::from(""),
            RETEST_BIN_NUM: Arc::from(""),
            PROCESS: Arc::from(""),
            UPLOAD_TIME: None,
        }
    }

    /// 填充文件信息
    /// 对应Scala中的fillFileInfo方法
    
    /// 填充文件信息到测试项详情 (对应Scala中的fillFileInfo方法)
    fn fill_file_info(test_item_detail: &mut DwsSubTestItemDetail, file_detail: &DwsSubFileDetail) {
        test_item_detail.CUSTOMER = file_detail.CUSTOMER.clone();
        test_item_detail.SUB_CUSTOMER = file_detail.SUB_CUSTOMER.clone();
        test_item_detail.UPLOAD_TYPE = file_detail.UPLOAD_TYPE.clone();
        test_item_detail.FILE_ID = file_detail.FILE_ID;
        test_item_detail.FILE_NAME = file_detail.FILE_NAME.clone();
        test_item_detail.FILE_TYPE = file_detail.FILE_TYPE.clone();
        test_item_detail.FACTORY = file_detail.FACTORY.clone();
        test_item_detail.FACTORY_SITE = file_detail.FACTORY_SITE.clone();
        test_item_detail.FAB = file_detail.FAB.clone();
        test_item_detail.FAB_SITE = file_detail.FAB_SITE.clone();
        test_item_detail.LOT_TYPE = file_detail.LOT_TYPE.clone();
        test_item_detail.TEST_AREA = file_detail.TEST_AREA.clone();
        test_item_detail.OFFLINE_RETEST = file_detail.OFFLINE_RETEST;
        test_item_detail.INTERRUPT = file_detail.INTERRUPT;
        test_item_detail.DUP_RETEST = file_detail.DUP_RETEST;
        test_item_detail.BATCH_NUM = file_detail.BATCH_NUM;
        test_item_detail.LOT_ID = file_detail.LOT_ID.clone();
        test_item_detail.SBLOT_ID = file_detail.SBLOT_ID.clone();
        test_item_detail.PROBER_HANDLER_ID = file_detail.PROBER_HANDLER_ID.clone();
        test_item_detail.TESTER_TYPE = file_detail.TESTER_TYPE.clone();
        test_item_detail.TEST_STAGE = file_detail.TEST_STAGE.clone();
        test_item_detail.DEVICE_ID = file_detail.DEVICE_ID.clone();
        test_item_detail.TEST_PROGRAM = file_detail.TEST_PROGRAM.clone();
        test_item_detail.TEST_TEMPERATURE = file_detail.TEST_TEMPERATURE.clone();
        test_item_detail.TEST_PROGRAM_VERSION = file_detail.TEST_PROGRAM_VERSION.clone();
        test_item_detail.TESTER_NAME = file_detail.TESTER_NAME.clone();
        test_item_detail.PROBECARD_LOADBOARD_ID = file_detail.PROBECARD_LOADBOARD_ID.clone();
        test_item_detail.START_TIME = file_detail.START_TIME;
        test_item_detail.END_TIME = file_detail.END_TIME;
        test_item_detail.START_HOUR_KEY = file_detail.START_HOUR_KEY.clone();
        test_item_detail.START_DAY_KEY = file_detail.START_DAY_KEY.clone();
        test_item_detail.END_HOUR_KEY = file_detail.END_HOUR_KEY.clone();
        test_item_detail.END_DAY_KEY = file_detail.END_DAY_KEY.clone();
        test_item_detail.FLOW_ID = file_detail.FLOW_ID.clone();
        test_item_detail.RETEST_BIN_NUM = file_detail.RETEST_BIN_NUM.clone();
        test_item_detail.PROCESS = file_detail.PROCESS.clone();
        test_item_detail.UPLOAD_TIME = file_detail.UPLOAD_TIME;
    }

    /// 将字符串集合去重并用逗号连接
    /// 对应Scala中的DwsCommonUtil.mkStringDistinct方法
    pub fn mk_string_distinct(strings: &[&String]) -> String {
        let mut unique_strings: Vec<String> = strings
            .iter()
            .filter(|s| !s.is_empty())
            .map(|s| s.to_string())
            .collect::<std::collections::HashSet<_>>()
            .into_iter()
            .collect();

        unique_strings.sort();
        unique_strings.join(",")
    }

    /// 判断是否为CP测试区域
    /// 参考Scala中的SUPPORT_CP_TEST_AREA_LIST.contains(TestArea.of(testArea))
    /// 对应Scala中的DwsService.isCpTestArea方法
    pub fn is_cp_test_area(test_area: &str) -> bool {
        use crate::model::constant::test_area::TestArea;

        // 获取CP测试区域列表
        let cp_test_area_list = TestArea::get_cp_list();

        // 将字符串转换为TestArea枚举
        if let Some(test_area_enum) = TestArea::of(test_area) {
            // 检查是否在CP测试区域列表中
            cp_test_area_list.contains(&test_area_enum)
        } else {
            // 如果无法识别的测试区域，默认返回false
            false
        }
    }
}

/// DWS子Die详情
/// 对应Scala中的SubFileDetail类
#[derive(Debug, Clone)]
#[allow(non_snake_case)]
pub struct DwsSubFileDetail {
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub FILE_ID: Option<i64>,
    pub FILE_NAME: Arc<str>,
    pub FILE_TYPE: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub OFFLINE_RETEST: Option<i32>,
    pub INTERRUPT: Option<i32>,
    pub DUP_RETEST: Option<i32>,
    pub BATCH_NUM: Option<i32>,
    pub LOT_ID: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub PROBER_HANDLER_ID: Arc<str>,
    pub TESTER_TYPE: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TESTER_NAME: Arc<str>,
    pub PROBECARD_LOADBOARD_ID: Arc<str>,
    pub START_TIME: Option<i64>,
    pub END_TIME: Option<i64>,
    pub START_HOUR_KEY: Arc<str>,
    pub START_DAY_KEY: Arc<str>,
    pub END_HOUR_KEY: Arc<str>,
    pub END_DAY_KEY: Arc<str>,
    pub FLOW_ID: Arc<str>,
    pub RETEST_BIN_NUM: Arc<str>,
    pub PROCESS: Arc<str>,
    pub UPLOAD_TIME: Option<i64>,
}

/// 对应Scala中的SubDieDetail类
#[derive(Debug, Clone)]
#[allow(non_snake_case)]
pub struct DwsSubDieDetail {
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub FILE_ID: Option<i64>,
    pub FILE_NAME: Arc<str>,
    pub FILE_TYPE: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub ECID: Arc<str>,
    pub IS_STANDARD_ECID: Option<i32>,
    pub SITE: Option<i64>,
    pub OFFLINE_RETEST: Option<i32>,
    pub OFFLINE_RETEST_IGNORE_TP: Option<i32>,
    pub ONLINE_RETEST: Option<i32>,
    pub INTERRUPT: Option<i32>,
    pub INTERRUPT_IGNORE_TP: Option<i32>,
    pub DUP_RETEST: Option<i32>,
    pub DUP_RETEST_IGNORE_TP: Option<i32>,
    pub BATCH_NUM: Option<i32>,
    pub BATCH_NUM_IGNORE_TP: Option<i32>,
    pub IS_FIRST_TEST: Option<i32>,
    pub IS_FINAL_TEST: Option<i32>,
    pub IS_FIRST_TEST_IGNORE_TP: Option<i32>,
    pub IS_FINAL_TEST_IGNORE_TP: Option<i32>,
    pub IS_DUP_FIRST_TEST: Option<i32>,
    pub IS_DUP_FINAL_TEST: Option<i32>,
    pub IS_DUP_FIRST_TEST_IGNORE_TP: Option<i32>,
    pub IS_DUP_FINAL_TEST_IGNORE_TP: Option<i32>,
    pub WAFER_ID: Arc<str>,
    pub WAFER_NO: Arc<str>,
    pub LOT_ID: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub WAFER_LOT_ID: Arc<str>,
    pub PROBER_HANDLER_ID: Arc<str>,
    pub TESTER_TYPE: Arc<str>,
    pub OPERATOR_NAME: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TESTER_NAME: Arc<str>,
    pub PROBECARD_LOADBOARD_ID: Arc<str>,
    pub FLOW_ID: Arc<str>,
    pub FLOW_ID_IGNORE_TP: Arc<str>,
    pub DIE_CNT: Option<i64>,
    pub X_COORD: Option<i32>,
    pub Y_COORD: Option<i32>,
    pub DIE_X: Option<i32>,
    pub DIE_Y: Option<i32>,
    pub WF_FLAT: Arc<str>,
    pub FABWF_ID: Arc<str>,
    pub POS_X: Arc<str>,
    pub POS_Y: Arc<str>,
    pub RETICLE_X: Option<i32>,
    pub RETICLE_Y: Option<i32>,
    pub SITE_ID: Arc<str>,
    pub TEST_TIME: Option<i64>,
    pub PART_ID: Arc<str>,
    pub SBIN_NUM: Option<i64>,
    pub SBIN_PF: Arc<str>,
    pub SBIN_NAM: Arc<str>,
    pub HBIN_NUM: Option<i64>,
    pub HBIN_PF: Arc<str>,
    pub HBIN_NAM: Arc<str>,
    pub HBIN: Arc<str>,
    pub SBIN: Arc<str>,
    pub START_TIME: Option<i64>,
    pub END_TIME: Option<i64>,
    pub START_HOUR_KEY: Arc<str>,
    pub START_DAY_KEY: Arc<str>,
    pub END_HOUR_KEY: Arc<str>,
    pub END_DAY_KEY: Arc<str>,
    pub C_PART_ID: Option<i64>,
    pub RETEST_BIN_NUM: Arc<str>,
    pub PROCESS: Arc<str>,
    pub CREATE_TIME: Option<i64>,
    pub UPLOAD_TIME: Option<i64>,
    pub TEST_HEAD: Option<i64>,
    pub PROBER_HANDLER_TYP: Arc<str>,
    pub PROBECARD_LOADBOARD_TYP: Arc<str>,
}

/// DWS子测试项详情
/// 对应Scala中的SubTestItemDetail类
#[derive(Debug, Clone)]
#[allow(non_snake_case)]
pub struct DwsSubTestItemDetail {
    // 原始测试项详情字段
    pub FILE_ID: Option<i64>,
    pub ONLINE_RETEST: Option<i32>,
    pub MAX_OFFLINE_RETEST: Option<i32>,
    pub MAX_ONLINE_RETEST: Option<i32>,
    pub IS_DIE_FIRST_TEST: Option<i32>,
    pub IS_DIE_FINAL_TEST: Option<i32>,
    pub IS_FIRST_TEST: Option<i32>,
    pub IS_FINAL_TEST: Option<i32>,
    pub IS_FIRST_TEST_IGNORE_TP: Option<i32>,
    pub IS_FINAL_TEST_IGNORE_TP: Option<i32>,
    pub IS_DUP_FIRST_TEST: Option<i32>,
    pub IS_DUP_FINAL_TEST: Option<i32>,
    pub IS_DUP_FIRST_TEST_IGNORE_TP: Option<i32>,
    pub IS_DUP_FINAL_TEST_IGNORE_TP: Option<i32>,
    pub TEST_SUITE: Arc<str>,
    pub CONDITION_SET: Option<std::collections::HashMap<String, String>>,
    pub TEST_NUM: Option<i64>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,
    pub IS_DIE_FIRST_TEST_ITEM: Option<i32>,
    pub TESTITEM_TYPE: Arc<str>,
    pub TEST_FLG: Arc<str>,
    pub PARM_FLG: Arc<str>,
    pub TEST_STATE: Arc<str>,
    pub TEST_VALUE: Option<f64>,
    pub UNITS: Arc<str>,
    pub TEST_RESULT: Option<i32>,
    pub ORIGIN_TEST_VALUE: Option<f64>,
    pub ORIGIN_UNITS: Arc<str>,
    pub TEST_ORDER: Option<i64>,
    pub ALARM_ID: Arc<str>,
    pub OPT_FLG: Arc<str>,
    pub RES_SCAL: Option<i32>,
    pub NUM_TEST: Option<i32>,
    pub LLM_SCAL: Option<i32>,
    pub HLM_SCAL: Option<i32>,
    pub LO_LIMIT: Option<f64>,
    pub HI_LIMIT: Option<f64>,
    pub ORIGIN_HI_LIMIT: Option<f64>,
    pub ORIGIN_LO_LIMIT: Option<f64>,
    pub C_RESFMT: Arc<str>,
    pub C_LLMFMT: Arc<str>,
    pub C_HLMFMT: Arc<str>,
    pub LO_SPEC: Option<f64>,
    pub HI_SPEC: Option<f64>,
    pub HBIN_NUM: Option<i64>,
    pub SBIN_NUM: Option<i64>,
    pub SBIN_PF: Arc<str>,
    pub SBIN_NAM: Arc<str>,
    pub HBIN_PF: Arc<str>,
    pub HBIN_NAM: Arc<str>,
    pub HBIN: Arc<str>,
    pub SBIN: Arc<str>,
    pub TEST_HEAD: Option<i64>,
    pub PART_FLG: Arc<str>,
    pub PART_ID: Arc<str>,
    pub C_PART_ID: Option<i64>,
    pub ECID: Arc<str>,
    pub ECID_EXT: Arc<str>,
    pub ECID_EXTRA: Option<std::collections::HashMap<String, String>>,
    pub IS_STANDARD_ECID: Option<i32>,
    pub X_COORD: Option<i32>,
    pub Y_COORD: Option<i32>,
    pub DIE_X: Option<i32>,
    pub DIE_Y: Option<i32>,
    pub TEST_TIME: Option<i64>,
    pub PART_TXT: Arc<str>,
    pub PART_FIX: Arc<str>,
    pub SITE: Option<i64>,
    pub TOUCH_DOWN_ID: Option<i32>,
    pub WAFER_LOT_ID: Arc<str>,
    pub WAFER_ID: Arc<str>,
    pub WAFER_NO: Arc<str>,
    pub RETICLE_T_X: Option<i32>,
    pub RETICLE_T_Y: Option<i32>,
    pub RETICLE_X: Option<i32>,
    pub RETICLE_Y: Option<i32>,
    pub SITE_ID: Arc<str>,
    pub VECT_NAM: Arc<str>,
    pub TIME_SET: Arc<str>,
    pub NUM_FAIL: Option<i64>,
    pub FAIL_PIN: Arc<str>,
    pub CYCL_CNT: Option<i64>,
    pub REPT_CNT: Option<i64>,
    pub LONG_ATTRIBUTE_SET: Option<std::collections::HashMap<String, i64>>,
    pub STRING_ATTRIBUTE_SET: Option<std::collections::HashMap<String, String>>,
    pub FLOAT_ATTRIBUTE_SET: Option<std::collections::HashMap<String, f64>>,
    pub UID: Arc<str>,
    pub TEXT_DAT: Arc<str>,
    pub CREATE_HOUR_KEY: Arc<str>,
    pub CREATE_DAY_KEY: Arc<str>,
    pub CREATE_TIME: Option<i64>,
    pub EFUSE_EXTRA: Option<std::collections::HashMap<String, String>>,
    pub CHIP_ID: Arc<str>,
    
    // 从文件信息中填充的字段
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub FILE_NAME: Arc<str>,
    pub FILE_TYPE: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub OFFLINE_RETEST: Option<i32>,
    pub INTERRUPT: Option<i32>,
    pub DUP_RETEST: Option<i32>,
    pub BATCH_NUM: Option<i32>,
    pub LOT_ID: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub PROBER_HANDLER_ID: Arc<str>,
    pub TESTER_TYPE: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TESTER_NAME: Arc<str>,
    pub OPERATOR_NAME: Arc<str>,
    pub PROBECARD_LOADBOARD_ID: Arc<str>,
    pub START_TIME: Option<i64>,
    pub END_TIME: Option<i64>,
    pub START_HOUR_KEY: Arc<str>,
    pub START_DAY_KEY: Arc<str>,
    pub END_HOUR_KEY: Arc<str>,
    pub END_DAY_KEY: Arc<str>,
    pub FLOW_ID: Arc<str>,
    pub RETEST_BIN_NUM: Arc<str>,
    pub PROCESS: Arc<str>,
    pub UPLOAD_TIME: Option<i64>,
}
