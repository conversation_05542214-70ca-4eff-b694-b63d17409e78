use crate::dto::dws::bin_failitem_index::BinFailitemIndex;
use crate::dto::dws::item_order::ItemOrder;
use crate::dto::dws::sub_bin_failitem::SubBinFailitem;
use crate::dws::dws_service::{DwsSubDieDetail, DwsSubTestItemDetail};
use crate::model::constant::{COMMA, EMPTY, MIDDLE_LINE, SYSTEM};
use crate::utils::date;
use std::collections::HashMap;
use chrono::Utc;


#[derive(Debug, Clone)]
pub struct BinFailitemIndexCommonService;

impl BinFailitemIndexCommonService {
    pub fn new() -> Self {
        Self
    }

    /// Generate file to min pass die map from die details
    /// Corresponds to generateFileToMinPassDieMap in Scala
    pub fn generate_file_to_min_pass_die_map(
        die_detail: &Vec<DwsSubDieDetail>,
    ) -> Option<(i64, i64)> {
        if die_detail.is_empty() {
            return None;
        }

        let min_die = die_detail
            .iter()
            .min_by_key(|d| {
                (
                    d.HBIN_NUM.unwrap_or(i64::MAX),
                    d.SBIN_NUM.unwrap_or(i64::MAX),
                    d.C_PART_ID.unwrap_or(i64::MAX),
                )
            })?;

        Some((
            min_die.FILE_ID.unwrap_or(0),
            min_die.C_PART_ID.unwrap_or(0),
        ))
    }

    /// Generate file to item order map from test item details
    /// Corresponds to generateFileToItemOrderMap in Scala
    pub fn generate_file_to_item_order_map(
        file_id: i64,
        test_item_detail: &Vec<DwsSubTestItemDetail>,
    ) -> (i64, Vec<ItemOrder>) {
        let mut item_orders: HashMap<String, ItemOrder> = HashMap::new();

        // Convert DwsSubTestItemDetail to ItemOrder and group by TEST_ITEM
        for detail in test_item_detail {
            let item_order = ItemOrder {
                TEST_NUM: detail.TEST_NUM,
                TEST_TXT: Some(detail.TEST_TXT.to_string()),
                TESTITEM_TYPE: Some(detail.TESTITEM_TYPE.to_string()),
                TEST_ITEM: Some(detail.TEST_ITEM.to_string()),
                TEST_ORDER: detail.TEST_ORDER,
                UNITS: Some(detail.UNITS.to_string()),
                ORIGIN_UNITS: Some(detail.ORIGIN_UNITS.to_string()),
            };

            // Keep the one with minimum TEST_ORDER for each TEST_ITEM
            item_orders
                .entry(detail.TEST_ITEM.to_string())
                .and_modify(|existing| {
                    if item_order.TEST_ORDER.unwrap_or(i64::MAX)
                        < existing.TEST_ORDER.unwrap_or(i64::MAX)
                    {
                        *existing = item_order.clone();
                    }
                })
                .or_insert(item_order);
        }

        // Sort by TEST_ORDER
        let mut result: Vec<ItemOrder> = item_orders.into_values().collect();
        result.sort_by_key(|item| item.TEST_ORDER.unwrap_or(i64::MAX));

        (file_id, result)
    }

    /// Calculate SubBinFailitem index from test item details and item orders
    pub fn calculate_sub_bin_failitem_index(
        items: &[&DwsSubTestItemDetail],
        item_orders: &[ItemOrder]
    ) -> Option<SubBinFailitem> {
        if items.is_empty() {
            return None;
        }

        let first_item = items[0];
        let mut fail_item_orders: Vec<ItemOrder> = Vec::new();
        let mut fail_item_order_valid_flag = 0;

        if !item_orders.is_empty() {
            // Get failed test items (TEST_RESULT != 1)
            let fail_items: std::collections::HashSet<String> = items
                .iter()
                .filter(|t| {
                    t.TEST_RESULT.is_some()
                        && t.TEST_RESULT != Some(1)
                })
                .map(|t| t.TEST_ITEM.to_string())
                .collect();

            fail_item_orders = item_orders
                .iter()
                .filter(|item| {
                    item.TEST_ITEM
                        .as_ref()
                        .map_or(false, |test_item| fail_items.contains(&test_item.to_string()))
                })
                .cloned()
                .collect();
            fail_item_order_valid_flag = 1;
        } else {
            // Create ItemOrders from failed test items directly
            let mut item_map: HashMap<String, ItemOrder> = HashMap::new();
            for detail in items {
                if detail.TEST_RESULT.is_some()
                    && detail.TEST_RESULT != Some(1)
                {
                    let test_item = &detail.TEST_ITEM;
                    let item_order = ItemOrder {
                        TEST_NUM: detail.TEST_NUM,
                        TEST_TXT: Some(detail.TEST_TXT.to_string()),
                        TESTITEM_TYPE: Some(detail.TESTITEM_TYPE.to_string()),
                        TEST_ITEM: Some(detail.TEST_ITEM.to_string()),
                        TEST_ORDER: detail.TEST_ORDER,
                        UNITS: Some(detail.UNITS.to_string()),
                        ORIGIN_UNITS: Some(detail.ORIGIN_UNITS.to_string()),
                    };

                    item_map
                        .entry(test_item.to_string())
                        .and_modify(|existing| {
                            if item_order.TEST_ORDER.unwrap_or(i64::MAX)
                                < existing.TEST_ORDER.unwrap_or(i64::MAX)
                            {
                                *existing = item_order.clone();
                            }
                        })
                        .or_insert(item_order);
                }
            }
            fail_item_orders = item_map.into_values().collect();
            fail_item_orders.sort_by_key(|item| item.TEST_ORDER.unwrap_or(i64::MAX));
        }

        let first_fail_item = if !fail_item_orders.is_empty() {
            Some(fail_item_orders[0].clone())
        } else {
            None
        };

        let last_fail_item = if !fail_item_orders.is_empty() {
            Some(fail_item_orders[fail_item_orders.len() - 1].clone())
        } else {
            None
        };

        Some(SubBinFailitem {
            CUSTOMER: Some(first_item.CUSTOMER.to_string()),
            SUB_CUSTOMER: Some(first_item.SUB_CUSTOMER.to_string()),
            UPLOAD_TYPE: Some(first_item.UPLOAD_TYPE.to_string()),
            FACTORY: Some(first_item.FACTORY.to_string()),
            FACTORY_SITE: Some(first_item.FACTORY_SITE.to_string()),
            FAB: Some(first_item.FAB.to_string()),
            FAB_SITE: Some(first_item.FAB_SITE.to_string()),
            TEST_AREA: Some(first_item.TEST_AREA.to_string()),
            TEST_STAGE: Some(first_item.TEST_STAGE.to_string()),
            LOT_TYPE: Some(first_item.LOT_TYPE.to_string()),
            DEVICE_ID: Some(first_item.DEVICE_ID.to_string()),
            LOT_ID: Some(first_item.LOT_ID.to_string()),
            SBLOT_ID: Some(first_item.SBLOT_ID.to_string()),
            WAFER_LOT_ID: Some(first_item.WAFER_LOT_ID.to_string()),
            WAFER_ID: Some(first_item.WAFER_ID.to_string()),
            WAFER_NO: Some(first_item.WAFER_NO.to_string()),
            TEST_PROGRAM: Some(first_item.TEST_PROGRAM.to_string()),
            TEST_TEMPERATURE: Some(first_item.TEST_TEMPERATURE.to_string()),
            TEST_PROGRAM_VERSION: Some(first_item.TEST_PROGRAM_VERSION.to_string()),
            FILE_ID: first_item.FILE_ID,
            FILE_NAME: Some(first_item.FILE_NAME.to_string()),
            FILE_TYPE: Some(first_item.FILE_TYPE.to_string()),
            TESTER_NAME: Some(first_item.TESTER_NAME.to_string()),
            TESTER_TYPE: Some(first_item.TESTER_TYPE.to_string()),
            PROBER_HANDLER_ID: Some(first_item.PROBER_HANDLER_ID.to_string()),
            PROBECARD_LOADBOARD_ID: Some(first_item.PROBECARD_LOADBOARD_ID.to_string()),
            START_TIME: first_item.START_TIME,
            END_TIME: first_item.END_TIME,
            START_HOUR_KEY: Some(first_item.START_HOUR_KEY.to_string()),
            START_DAY_KEY: Some(first_item.START_DAY_KEY.to_string()),
            END_HOUR_KEY: Some(first_item.END_HOUR_KEY.to_string()),
            END_DAY_KEY: Some(first_item.END_DAY_KEY.to_string()),
            FLOW_ID: Some(first_item.FLOW_ID.to_string()),
            ONLINE_RETEST: first_item.ONLINE_RETEST,
            ECID: Some(first_item.ECID.to_string()),
            HBIN_NUM: first_item.HBIN_NUM,
            HBIN_NAM: Some(first_item.HBIN_NAM.to_string()),
            HBIN_PF: Some(first_item.HBIN_PF.to_string()),
            HBIN: Some(first_item.HBIN.to_string()),
            SBIN_NUM: first_item.SBIN_NUM,
            SBIN_NAM: Some(first_item.SBIN_NAM.to_string()),
            SBIN_PF: Some(first_item.SBIN_PF.to_string()),
            SBIN: Some(first_item.SBIN.to_string()),
            FIRST_FAIL_ITEM: first_fail_item,
            LAST_FAIL_ITEM: last_fail_item,
            ALL_FAIL_ITEM: if fail_item_orders.is_empty() {
                None
            } else {
                Some(fail_item_orders)
            },
            ORDER_VALID_FLAG: Some(fail_item_order_valid_flag),
            PROCESS: Some(first_item.PROCESS.to_string()),
            UPLOAD_TIME: first_item.UPLOAD_TIME,
        })
    }

    /// Generate CP final flag from SubBinFailitem records
    /// Corresponds to generateCpFinalFlag in Scala
    pub fn generate_cp_final_flag(
        records: &mut Vec<SubBinFailitem>,
    ) -> Vec<(SubBinFailitem, i32)> {
        if records.is_empty() {
            return Vec::new();
        }

        // Sort by ONLINE_RETEST
        records.sort_by_key(|r| r.ONLINE_RETEST.unwrap_or(i32::MAX));

        let mut result = Vec::new();
        if !records.is_empty() {
            // First record gets finalFlag=0, last gets finalFlag=1
            result.push((records[0].clone(), 0));
            result.push((records[records.len() - 1].clone(), 1));
        }

        result
    }

    /// Convert ItemOrder to item detail string
    /// Corresponds to toItemDetail(itemOrder: ItemOrder) in Scala
    pub fn to_item_detail(item_order: &ItemOrder) -> String {
        format!(
            "{}{}{}",
            item_order.TESTITEM_TYPE.as_deref().unwrap_or(""),
            MIDDLE_LINE,
            item_order.TEST_ITEM.as_deref().unwrap_or("")
        )
    }

    /// Convert ItemOrder list to item detail string list
    /// Corresponds to toItemDetail(itemOrders: List[ItemOrder]) in Scala
    pub fn to_item_detail_list(item_orders: &Vec<ItemOrder>) -> Vec<String> {
        item_orders.iter().map(Self::to_item_detail).collect()
    }

    /// Calculate BinFailitemIndex from SubBinFailitem records
    /// Corresponds to calculateBinFailitemIndex in Scala
    pub fn calculate_bin_failitem_index(
        sub_bin_failitems: &Vec<SubBinFailitem>,
        final_flag: i32,
    ) -> Option<BinFailitemIndex> {
        if sub_bin_failitems.is_empty() {
            return None;
        }

        let head = &sub_bin_failitems[0];

        // Collect first fail items (distinct)
        let mut first_fail_items: Vec<ItemOrder> = Vec::new();
        let mut seen_first: std::collections::HashSet<String> = std::collections::HashSet::new();

        // Collect all fail items (distinct)
        let mut all_fail_items: Vec<ItemOrder> = Vec::new();
        let mut seen_all: std::collections::HashSet<String> = std::collections::HashSet::new();

        // Collect last fail items
        let mut last_fail_items: Vec<ItemOrder> = Vec::new();

        for sub_bin in sub_bin_failitems {
            if let Some(ref all_fail_item) = sub_bin.ALL_FAIL_ITEM {
                if !all_fail_item.is_empty() {
                    // First fail item
                    if let Some(ref first_fail) = sub_bin.FIRST_FAIL_ITEM {
                        if let Some(ref test_item) = first_fail.TEST_ITEM {
                            if !seen_first.contains(test_item) {
                                first_fail_items.push(first_fail.clone());
                                seen_first.insert(test_item.clone());
                            }
                        }
                    }

                    // All fail items
                    for item in all_fail_item {
                        if let Some(ref test_item) = item.TEST_ITEM {
                            if !seen_all.contains(test_item) {
                                all_fail_items.push(item.clone());
                                seen_all.insert(test_item.clone());
                            }
                        }
                    }

                    // Last fail item
                    if let Some(ref last_fail) = sub_bin.LAST_FAIL_ITEM {
                        last_fail_items.push(last_fail.clone());
                    }
                }
            }
        }

        let first_failitem_cnt = first_fail_items.len() as i64;
        let all_failitem_cnt = all_fail_items.len() as i64;

        let mut first_failitem_detail = EMPTY.to_string();
        let mut last_failitem_of_all = EMPTY.to_string();
        let mut all_failitem_detail = EMPTY.to_string();

        if head.ORDER_VALID_FLAG == Some(1) && !last_fail_items.is_empty() {
            // Sort first fail items by TEST_ORDER
            first_fail_items.sort_by_key(|item| item.TEST_ORDER.unwrap_or(i64::MAX));
            first_failitem_detail = first_fail_items
                .iter()
                .map(Self::to_item_detail)
                .collect::<Vec<_>>()
                .join(COMMA);

            // Get last fail item with max TEST_ORDER
            if let Some(last_item) = last_fail_items
                .iter()
                .max_by_key(|item| item.TEST_ORDER.unwrap_or(i64::MIN))
            {
                last_failitem_of_all = Self::to_item_detail(last_item);
            }

            // Sort all fail items by TEST_ORDER and take first 100
            all_fail_items.sort_by_key(|item| item.TEST_ORDER.unwrap_or(i64::MAX));
            all_failitem_detail = all_fail_items
                .iter()
                .take(100)
                .map(Self::to_item_detail)
                .collect::<Vec<_>>()
                .join(COMMA);
        }

        let now = Utc::now();
        let create_hour_key = date::get_day_hour(now);
        let create_day_key = date::get_day(now);

        Some(BinFailitemIndex {
            CUSTOMER: head.CUSTOMER.clone(),
            SUB_CUSTOMER: head.SUB_CUSTOMER.clone(),
            UPLOAD_TYPE: head.UPLOAD_TYPE.clone(),
            FACTORY: head.FACTORY.clone(),
            FACTORY_SITE: head.FACTORY_SITE.clone(),
            FAB: head.FAB.clone(),
            FAB_SITE: head.FAB_SITE.clone(),
            TEST_AREA: head.TEST_AREA.clone(),
            TEST_STAGE: head.TEST_STAGE.clone(),
            LOT_TYPE: head.LOT_TYPE.clone(),
            DEVICE_ID: head.DEVICE_ID.clone(),
            LOT_ID: head.LOT_ID.clone(),
            SBLOT_ID: head.SBLOT_ID.clone(),
            WAFER_LOT_ID: head.WAFER_LOT_ID.clone(),
            WAFER_ID: head.WAFER_ID.clone(),
            WAFER_ID_KEY: head.WAFER_ID.clone(),
            WAFER_NO: head.WAFER_NO.clone(),
            WAFER_NO_KEY: head.WAFER_NO.clone(),
            TEST_PROGRAM: head.TEST_PROGRAM.clone(),
            TEST_TEMPERATURE: head.TEST_TEMPERATURE.clone(),
            TEST_PROGRAM_VERSION: head.TEST_PROGRAM_VERSION.clone(),
            FILE_ID: head.FILE_ID,
            FILE_NAME: head.FILE_NAME.clone(),
            FILE_TYPE: head.FILE_TYPE.clone(),
            TESTER_NAME: head.TESTER_NAME.clone(),
            TESTER_TYPE: head.TESTER_TYPE.clone(),
            PROBER_HANDLER_ID: head.PROBER_HANDLER_ID.clone(),
            PROBECARD_LOADBOARD_ID: head.PROBECARD_LOADBOARD_ID.clone(),
            START_TIME: head.START_TIME,
            END_TIME: head.END_TIME,
            START_HOUR_KEY: head.START_HOUR_KEY.clone(),
            START_DAY_KEY: head.START_DAY_KEY.clone(),
            END_HOUR_KEY: head.END_HOUR_KEY.clone(),
            END_DAY_KEY: head.END_DAY_KEY.clone(),
            FLOW_ID: head.FLOW_ID.clone(),
            FINAL_FLAG: Some(final_flag as i32),
            HBIN_NUM: head.HBIN_NUM,
            HBIN_NUM_KEY: Self::string_value(head.HBIN_NUM),
            HBIN_NAM: head.HBIN_NAM.clone(),
            HBIN_PF: head.HBIN_PF.clone(),
            HBIN: head.HBIN.clone(),
            SBIN_NUM: head.SBIN_NUM,
            SBIN_NUM_KEY: Self::string_value(head.SBIN_NUM),
            SBIN_NAM: head.SBIN_NAM.clone(),
            SBIN_PF: head.SBIN_PF.clone(),
            SBIN: head.SBIN.clone(),
            FIRST_FAIL_ITEM_CNT: Some(first_failitem_cnt as i64),
            FIRST_FAIL_ITEM_DETAIL: Some(first_failitem_detail),
            LAST_FAIL_ITEM_OF_ALL: Some(last_failitem_of_all),
            ALL_FAIL_ITEM_CNT: Some(all_failitem_cnt as i64),
            ALL_FAIL_ITEM_DETAIL: Some(all_failitem_detail),
            CREATE_HOUR_KEY: Some(create_hour_key),
            CREATE_DAY_KEY: Some(create_day_key),
            CREATE_TIME: Some(now.timestamp_millis()),
            CREATE_USER: Some(SYSTEM.to_string()),
            VERSION: Some(now.timestamp_millis()),
            PROCESS: head.PROCESS.clone(),
            UPLOAD_TIME: head.UPLOAD_TIME,
        })
    }

    /// Helper function to convert Option<i64> to Option<String>
    fn string_value(value: Option<i64>) -> Option<String> {
        value.map(|v| v.to_string())
    }

}