//! BinTestItemIndexService 实现
//!
//! 对应Scala中的BinTestItemIndexService类，用于计算Bin测试项索引

use crate::dws::dws_service::{DwsService, DwsSubTestItemDetail};
use crate::utils::{date, stats};
use crate::utils::decimal::IntoDecimal38_18;
use chrono::DateTime;
use log::info;
use std::collections::HashMap;
use std::sync::Arc;

// 添加必要的导入
use arrow::record_batch::RecordBatch;
use serde_arrow::schema::{SchemaLike, TracingOptions};
use arrow::datatypes::FieldRef;
use crate::parquet::RecordBatchWrapper;
use crate::dws::model::{BinTestItemIndex, SubBinTestItemIndex};
use crate::model::constant::test_area::TestArea;
use crate::model::constant::{ALL, EMPTY, F, P, PF_PASS, SYSTEM};
use crate::model::constant::upload_type::UploadType;
use crate::dws::model::sblot_aggregation::{Sblot, Lot, aggregate_sblot_to_lot};

/// Bin测试项索引服务
/// 对应Scala中的com.guwave.onedata.dataware.dw.common.dws.table.distributed.BinTestItemIndexService
pub struct BinTestItemIndexService {
    test_area: String,
}

impl BinTestItemIndexService {
    /// 创建新的BinTestItemIndexService实例
    pub fn new(test_area: String) -> Self {
        Self { test_area }
    }

    /// 计算Bin测试项索引
    /// 对应Scala中的calculate方法
    pub fn calculate(
        &self,
        test_item_detail: &[Arc<DwsSubTestItemDetail>],
        retest_bin_num_map: Option<&HashMap<Sblot, HashMap<String, i32>>>,
    ) -> Vec<BinTestItemIndex> {
        info!("Calculating bin test item index for {} items", test_item_detail.len());
        
        let filter_test_item_detail: Vec<&DwsSubTestItemDetail> = test_item_detail
            .iter()
            .filter(|item| {
                item.IS_DUP_FINAL_TEST.unwrap_or(0) == 1 && item.TESTITEM_TYPE != Arc::from(F)
            })
            .map(|item| item.as_ref())
            .collect();

        if DwsService::is_cp_test_area(&self.test_area) {
            self.cp_calculate(&filter_test_item_detail)
        } else {
            self.ft_calculate(&filter_test_item_detail, retest_bin_num_map)
        }
    }

    /// 计算bin test item 通用index
    /// 对应Scala中的calculateBinTestItemIndex方法
    pub fn calculate_bin_test_item_index(
        &self,
        items: &[DwsSubTestItemDetail],
        is_wafer: bool,
        retest_bin_num_map: Option<&HashMap<String, i32>>,
    ) -> SubBinTestItemIndex {
        // 过滤出首次测试的项目
        let first_bin_test_items: Vec<&DwsSubTestItemDetail> = items
            .iter()
            .filter(|item| item.IS_FIRST_TEST.unwrap_or(0) == 1)
            .collect();

        // 计算最终测试项目
        let final_bin_test_items = self.calculate_final_bin_test_items(items, is_wafer, retest_bin_num_map);

        // 计算首次通过数量
        let first_pass_cnt = first_bin_test_items
            .iter()
            .filter(|item| item.TEST_RESULT.is_some() && item.TEST_RESULT.unwrap() == 1)
            .count() as i64;

        // 计算最终通过数量
        let final_pass_cnt = final_bin_test_items
            .iter()
            .filter(|item| item.TEST_RESULT.is_some() && item.TEST_RESULT.unwrap() == 1)
            .count() as i64;

        // 计算首次失败数量
        let first_fail_cnt = first_bin_test_items
            .iter()
            .filter(|item| item.TEST_RESULT.is_some() && item.TEST_RESULT.unwrap() != 1)
            .count() as i64;

        // 计算最终失败数量
        let final_fail_cnt = final_bin_test_items
            .iter()
            .filter(|item| item.TEST_RESULT.is_some() && item.TEST_RESULT.unwrap() != 1)
            .count() as i64;

        // 总数量
        let total_cnt = first_bin_test_items.len() as i64;

        // 获取开始和结束时间
        let start_time = items
            .iter()
            .filter_map(|item| item.START_TIME)
            .min();

        let end_time = items
            .iter()
            .filter_map(|item| item.END_TIME)
            .max();

        // 获取首次测试结果值（过滤掉TEST_RESULT >= 2的项目）
        let first_result: Vec<f64> = first_bin_test_items
            .iter()
            .filter(|item| {
                item.TEST_RESULT.is_some() && item.TEST_RESULT.unwrap() < 2 && item.TEST_VALUE.is_some()
            })
            .map(|item| item.TEST_VALUE.unwrap())
            .collect();

        // 获取最终测试结果值（过滤掉TEST_RESULT >= 2的项目）
        let final_result: Vec<f64> = final_bin_test_items
            .iter()
            .filter(|item| {
                item.TEST_RESULT.is_some() && item.TEST_RESULT.unwrap() < 2 && item.TEST_VALUE.is_some()
            })
            .map(|item| item.TEST_VALUE.unwrap())
            .collect();

        // 计算统计值 - 使用公共工具类方法
        let first_mean = stats::calculate_mean(&first_result);
        let first_sum = stats::calculate_sum(&first_result);
        let first_standard_square_sum = stats::calculate_square_sum(&first_result)
            .map(|sum| sum.sqrt());

        let final_mean = stats::calculate_mean(&final_result);
        let final_sum = stats::calculate_sum(&final_result);
        let final_standard_square_sum = stats::calculate_square_sum(&final_result)
            .map(|sum| sum.sqrt());

        // 计算CP和CPK值，使用安全的计算函数
        let first_item = items.first();
        let (first_cp, first_cpk) = if let Some(item) = first_item {
            let first_std_dev = stats::calculate_std_dev(&first_result);
            let lo_limit_opt = item.LO_LIMIT;
            let hi_limit_opt = item.HI_LIMIT;
            
            let cp = stats::calculate_cp_safe(first_std_dev, lo_limit_opt, hi_limit_opt);
            let cpk = stats::calculate_cpk_safe(first_mean, first_std_dev, lo_limit_opt, hi_limit_opt);
            (cp, cpk)
        } else {
            (None, None)
        };

        let (final_cp, final_cpk) = if let Some(item) = first_item {
            let final_std_dev = stats::calculate_std_dev(&final_result);
            let lo_limit_opt = item.LO_LIMIT;
            let hi_limit_opt = item.HI_LIMIT;
            
            let cp = stats::calculate_cp_safe(final_std_dev, lo_limit_opt, hi_limit_opt);
            let cpk = stats::calculate_cpk_safe(final_mean, final_std_dev, lo_limit_opt, hi_limit_opt);
            (cp, cpk)
        } else {
            (None, None)
        };

        // 计算时间键值 - 使用公共工具类方法
        let start_hour_key = start_time
            .map(|st| date::get_day_hour(DateTime::from_timestamp_millis(st).unwrap_or_default()))
            .unwrap_or_else(|| EMPTY.to_string());

        let start_day_key = start_time
            .map(|st| date::get_day(DateTime::from_timestamp_millis(st).unwrap_or_default()))
            .unwrap_or_else(|| EMPTY.to_string());

        let end_hour_key = end_time
            .map(|et| date::get_day_hour(DateTime::from_timestamp_millis(et).unwrap_or_default()))
            .unwrap_or_else(|| EMPTY.to_string());

        let end_day_key = end_time
            .map(|et| date::get_day(DateTime::from_timestamp_millis(et).unwrap_or_default()))
            .unwrap_or_else(|| EMPTY.to_string());

        // 收集并去重各种名称
        let tester_names = DwsService::mk_string_distinct(&items.iter().map(|item| item.TESTER_NAME.to_string()).collect::<Vec<_>>().iter().collect::<Vec<_>>());
        let tester_types = DwsService::mk_string_distinct(&items.iter().map(|item| item.TESTER_TYPE.to_string()).collect::<Vec<_>>().iter().collect::<Vec<_>>());
        let prober_handler_ids = DwsService::mk_string_distinct(&items.iter().map(|item| item.PROBER_HANDLER_ID.to_string()).collect::<Vec<_>>().iter().collect::<Vec<_>>());
        let prober_card_load_board_ids = DwsService::mk_string_distinct(&items.iter().map(|item| item.PROBECARD_LOADBOARD_ID.to_string()).collect::<Vec<_>>().iter().collect::<Vec<_>>());

        SubBinTestItemIndex {
            tester_names,
            tester_types,
            prober_handler_ids,
            prober_card_load_board_ids,
            start_time: start_time.map(|t| DateTime::from_timestamp_millis(t).unwrap_or_default()),
            end_time: end_time.map(|t| DateTime::from_timestamp_millis(t).unwrap_or_default()),
            start_hour_key,
            start_day_key,
            end_hour_key,
            end_day_key,
            first_pass_cnt,
            final_pass_cnt,
            first_fail_cnt,
            final_fail_cnt,
            total_cnt,
            first_mean,
            final_mean,
            first_sum,
            final_sum,
            first_standard_square_sum,
            final_standard_square_sum,
        }
    }

    /// CP测试区域的计算逻辑
    fn cp_calculate(&self, test_item_detail: &[&DwsSubTestItemDetail]) -> Vec<BinTestItemIndex> {
        let mut result = Vec::new();
        
        // 按照完整的BinTestItem key分组，对应Scala中的BinTestItem(Wafer(Lot(...), WAFER_ID), HBIN_PF, HBIN_NUM, HBIN_NAM, SBIN_PF, SBIN_NUM, SBIN_NAM, TEST_ITEM)
        let mut wafer_groups: HashMap<String, Vec<&DwsSubTestItemDetail>> = HashMap::new();
        for item in test_item_detail {
            // 构建完整的分组key，包含Lot信息、Wafer信息和Bin信息
            let wafer_key = format!(
                "{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}",
                item.CUSTOMER,           // Lot.customer
                item.FACTORY,            // Lot.factory  
                item.DEVICE_ID,          // Lot.deviceId
                item.TEST_AREA,          // Lot.testArea
                item.LOT_TYPE,           // Lot.lotType
                item.LOT_ID,             // Lot.lotId
                item.TEST_STAGE,         // Lot.testStage
                item.TEST_PROGRAM,       // Lot.testProgram
                item.WAFER_ID,           // Wafer.waferId
                item.HBIN_PF,            // BinTestItem.hbinPf
                item.HBIN_NUM.unwrap_or(0),  // BinTestItem.hbinNum
                item.HBIN_NAM,           // BinTestItem.hbinNam
                item.SBIN_PF,            // BinTestItem.sbinPf
                item.SBIN_NUM.unwrap_or(0),  // BinTestItem.sbinNum
                item.SBIN_NAM,           // BinTestItem.sbinNam
                item.TEST_ITEM           // BinTestItem.testItem
            );
            wafer_groups.entry(wafer_key).or_insert_with(Vec::new).push(item);
        }

        for (_, items) in wafer_groups {
            if !items.is_empty() {
                // 转换为Vec<DwsSubTestItemDetail>以便调用calculate_bin_test_item_index
                let items_owned: Vec<DwsSubTestItemDetail> = items.iter().map(|&item| item.clone()).collect();
                
                // 调用通用的calculate_bin_test_item_index方法
                let sub_index = self.calculate_bin_test_item_index(&items_owned, true, None);

                let now = chrono::Utc::now().timestamp_millis();
                let create_hour_key = date::get_day_hour(chrono::Utc::now());
                let create_day_key = date::get_day_hour(chrono::Utc::now());


                // 构建BinTestItemIndex
                let first_item = items[0];

                // 计算file_id
                let upload_type = UploadType::new(&first_item.UPLOAD_TYPE);
                let file_id = if upload_type == UploadType::AUTO {
                    0
                } else {
                    first_item.FILE_ID.unwrap_or(0)
                };
                
                let file_name = if upload_type == UploadType::AUTO {
                    EMPTY.to_string()
                } else {
                    first_item.FILE_NAME.to_string()
                };
                
                let bin_test_item_index = BinTestItemIndex {
                    CUSTOMER: first_item.CUSTOMER.to_string(),
                    SUB_CUSTOMER: first_item.SUB_CUSTOMER.to_string(),
                    UPLOAD_TYPE: first_item.UPLOAD_TYPE.to_string(),
                    FILE_ID: file_id,
                    FILE_NAME: file_name,
                    FACTORY: first_item.FACTORY.to_string(),
                    FACTORY_SITE: first_item.FACTORY_SITE.to_string(),
                    FAB: first_item.FAB.to_string(),
                    FAB_SITE: first_item.FAB_SITE.to_string(),
                    TEST_AREA: first_item.TEST_AREA.to_string(),
                    TEST_STAGE: first_item.TEST_STAGE.to_string(),
                    LOT_TYPE: first_item.LOT_TYPE.to_string(),
                    DEVICE_ID: first_item.DEVICE_ID.to_string(),
                    LOT_ID: first_item.LOT_ID.to_string(),
                    WAFER_ID: first_item.WAFER_ID.to_string(),
                    WAFER_ID_KEY: first_item.WAFER_ID.to_string(),
                    WAFER_NO: first_item.WAFER_NO.to_string(),
                    WAFER_NO_KEY: first_item.WAFER_NO.to_string(),
                    TEST_PROGRAM: first_item.TEST_PROGRAM.to_string(),
                    TEST_TEMPERATURE: first_item.TEST_TEMPERATURE.to_string(),
                    TESTER_NAME: sub_index.tester_names,
                    TESTER_TYPE: sub_index.tester_types,
                    PROBER_HANDLER_ID: sub_index.prober_handler_ids,
                    PROBECARD_LOADBOARD_ID: sub_index.prober_card_load_board_ids,
                    START_TIME: sub_index.start_time.map(|t| t.timestamp_millis()),
                    END_TIME: sub_index.end_time.map(|t| t.timestamp_millis()),
                    START_HOUR_KEY: sub_index.start_hour_key,
                    START_DAY_KEY: sub_index.start_day_key,
                    END_HOUR_KEY: sub_index.end_hour_key,
                    END_DAY_KEY: sub_index.end_day_key,
                    HBIN_NUM: first_item.HBIN_NUM.unwrap_or(0),
                    HBIN_NUM_KEY: first_item.HBIN_NUM.map(|n| n.to_string()).unwrap_or_default(),
                    SBIN_NUM: first_item.SBIN_NUM.unwrap_or(0),
                    SBIN_NUM_KEY: first_item.SBIN_NUM.map(|n| n.to_string()).unwrap_or_default(),
                    SBIN_PF: first_item.SBIN_PF.to_string(),
                    SBIN_NAM: first_item.SBIN_NAM.to_string(),
                    HBIN_PF: first_item.HBIN_PF.to_string(),
                    HBIN_NAM: first_item.HBIN_NAM.to_string(),
                    HBIN: first_item.HBIN.to_string(),
                    SBIN: first_item.SBIN.to_string(),
                    TESTITEM_TYPE: first_item.TESTITEM_TYPE.to_string(),
                    TEST_NUM: first_item.TEST_NUM.unwrap_or(0),
                    TEST_TXT: first_item.TEST_TXT.to_string(),
                    TEST_ITEM: first_item.TEST_ITEM.to_string(),
                    HI_SPEC: first_item.HI_SPEC.unwrap_or(0.0),
                    LO_SPEC: first_item.LO_SPEC.unwrap_or(0.0),
                    HI_LIMIT: first_item.HI_LIMIT.unwrap_or(0.0),
                    LO_LIMIT: first_item.LO_LIMIT.unwrap_or(0.0),
                    FIRST_PASS_CNT: sub_index.first_pass_cnt,
                    FINAL_PASS_CNT: sub_index.final_pass_cnt,
                    FIRST_FAIL_CNT: sub_index.first_fail_cnt,
                    FINAL_FAIL_CNT: sub_index.final_fail_cnt,
                    TOTAL_CNT: sub_index.total_cnt,
                    FIRST_MEAN: sub_index.first_mean,
                    FINAL_MEAN: sub_index.final_mean,
                    FIRST_SUM: sub_index.first_sum,
                    FINAL_SUM: sub_index.final_sum,
                    FIRST_STANDARD_SQUARE_SUM: sub_index.first_standard_square_sum,
                    FINAL_STANDARD_SQUARE_SUM: sub_index.final_standard_square_sum,
                    CREATE_HOUR_KEY: create_hour_key,
                    CREATE_DAY_KEY: create_day_key,
                    CREATE_TIME: now,
                    CREATE_USER: SYSTEM.to_string(),
                    VERSION: 1,
                    PROCESS: first_item.PROCESS.to_string(),
                    UPLOAD_TIME: first_item.UPLOAD_TIME.unwrap_or(0),
                };
                
                result.push(bin_test_item_index);
            }
        }
        
        result
    }

    /// FT测试区域的计算逻辑
    fn ft_calculate(
        &self,
        test_item_detail: &[&DwsSubTestItemDetail],
        sblot_retest_bin_num_map: Option<&HashMap<Sblot, HashMap<String, i32>>>,
    ) -> Vec<BinTestItemIndex> {
        let mut result = Vec::new();
        
        // 将sblot维度聚合成lot维度
        // 对应Scala中的: sblotRetestBinNumMap.groupBy { case (sblot, _) => Lot(...) }.mapValues(...)
        let lot_retest_bin_num_map: HashMap<Lot, HashMap<String, i32>> = if let Some(sblot_map) = sblot_retest_bin_num_map {
            aggregate_sblot_to_lot(sblot_map.clone())
        } else {
            HashMap::new()
        };
        
        // 按照完整的BinTestItem key分组，对应Scala中的BinTestItem(Wafer(Lot(...), EMPTY), HBIN_PF, HBIN_NUM, HBIN_NAM, SBIN_PF, SBIN_NUM, SBIN_NAM, TEST_ITEM)
        let mut lot_groups: HashMap<String, Vec<&DwsSubTestItemDetail>> = HashMap::new();
        for item in test_item_detail {
            // 构建完整的分组key，包含Lot信息、空的Wafer信息和Bin信息
            let lot_key = format!(
                "{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}",
                item.CUSTOMER,           // Lot.customer
                item.FACTORY,            // Lot.factory  
                item.DEVICE_ID,          // Lot.deviceId
                item.TEST_AREA,          // Lot.testArea
                item.LOT_TYPE,           // Lot.lotType
                item.LOT_ID,             // Lot.lotId
                item.TEST_STAGE,         // Lot.testStage
                item.TEST_PROGRAM,       // Lot.testProgram
                EMPTY,                   // EMPTY for Wafer.waferId (FT测试没有wafer信息)
                item.HBIN_PF,            // BinTestItem.hbinPf
                item.HBIN_NUM.unwrap_or(0),  // BinTestItem.hbinNum
                item.HBIN_NAM,           // BinTestItem.hbinNam
                item.SBIN_PF,            // BinTestItem.sbinPf
                item.SBIN_NUM.unwrap_or(0),  // BinTestItem.sbinNum
                item.SBIN_NAM,           // BinTestItem.sbinNam
                item.TEST_ITEM           // BinTestItem.testItem
            );
            lot_groups.entry(lot_key).or_insert_with(Vec::new).push(item);
        }

        for (_, items) in lot_groups {
            if !items.is_empty() {
                // 转换为Vec<DwsSubTestItemDetail>以便调用calculate_bin_test_item_index
                let items_owned: Vec<DwsSubTestItemDetail> = items.iter().map(|&item| item.clone()).collect();
                
                // 为FT测试准备retest_bin_num_map，使用聚合后的lot维度数据
                let first_item = items[0];
                let lot = Lot::from_test_item_detail(first_item);
                let current_retest_map = lot_retest_bin_num_map.get(&lot);
                
                // 调用通用的calculate_bin_test_item_index方法
                let sub_index = self.calculate_bin_test_item_index(&items_owned, false, current_retest_map);
                
                // 构建BinTestItemIndex
                let first_item = items[0];
                let now = chrono::Utc::now().timestamp_millis();
                let create_hour_key = date::get_day_hour(chrono::Utc::now());
                let create_day_key = date::get_day_hour(chrono::Utc::now());
                
                // 计算file_id
                let upload_type = UploadType::new(&first_item.UPLOAD_TYPE);
                let file_id = if upload_type == UploadType::AUTO {
                    0
                } else {
                    first_item.FILE_ID.unwrap_or(0)
                };
                
                let file_name = if upload_type == UploadType::AUTO {
                    EMPTY.to_string()
                } else {
                    first_item.FILE_NAME.to_string()
                };
                
                let bin_test_item_index = BinTestItemIndex {
                    CUSTOMER: first_item.CUSTOMER.to_string(),
                    SUB_CUSTOMER: first_item.SUB_CUSTOMER.to_string(),
                    UPLOAD_TYPE: first_item.UPLOAD_TYPE.to_string(),
                    FILE_ID: file_id,
                    FILE_NAME: file_name,
                    FACTORY: first_item.FACTORY.to_string(),
                    FACTORY_SITE: first_item.FACTORY_SITE.to_string(),
                    FAB: first_item.FAB.to_string(),
                    FAB_SITE: first_item.FAB_SITE.to_string(),
                    TEST_AREA: first_item.TEST_AREA.to_string(),
                    TEST_STAGE: first_item.TEST_STAGE.to_string(),
                    LOT_TYPE: first_item.LOT_TYPE.to_string(),
                    DEVICE_ID: first_item.DEVICE_ID.to_string(),
                    LOT_ID: first_item.LOT_ID.to_string(),
                    WAFER_ID: EMPTY.to_string(), // FT测试没有wafer信息
                    WAFER_ID_KEY: EMPTY.to_string(),
                    WAFER_NO: EMPTY.to_string(),
                    WAFER_NO_KEY: EMPTY.to_string(),
                    TEST_PROGRAM: first_item.TEST_PROGRAM.to_string(),
                    TEST_TEMPERATURE: first_item.TEST_TEMPERATURE.to_string(),
                    TESTER_NAME: sub_index.tester_names,
                    TESTER_TYPE: sub_index.tester_types,
                    PROBER_HANDLER_ID: sub_index.prober_handler_ids,
                    PROBECARD_LOADBOARD_ID: sub_index.prober_card_load_board_ids,
                    START_TIME: sub_index.start_time.map(|t| t.timestamp_millis()),
                    END_TIME: sub_index.end_time.map(|t| t.timestamp_millis()),
                    START_HOUR_KEY: sub_index.start_hour_key,
                    START_DAY_KEY: sub_index.start_day_key,
                    END_HOUR_KEY: sub_index.end_hour_key,
                    END_DAY_KEY: sub_index.end_day_key,
                    HBIN_NUM: first_item.HBIN_NUM.unwrap_or(0),
                    HBIN_NUM_KEY: first_item.HBIN_NUM.map(|n| n.to_string()).unwrap_or_default(),
                    SBIN_NUM: first_item.SBIN_NUM.unwrap_or(0),
                    SBIN_NUM_KEY: first_item.SBIN_NUM.map(|n| n.to_string()).unwrap_or_default(),
                    SBIN_PF: first_item.SBIN_PF.to_string(),
                    SBIN_NAM: first_item.SBIN_NAM.to_string(),
                    HBIN_PF: first_item.HBIN_PF.to_string(),
                    HBIN_NAM: first_item.HBIN_NAM.to_string(),
                    HBIN: first_item.HBIN.to_string(),
                    SBIN: first_item.SBIN.to_string(),
                    TESTITEM_TYPE: first_item.TESTITEM_TYPE.to_string(),
                    TEST_NUM: first_item.TEST_NUM.unwrap_or(0),
                    TEST_TXT: first_item.TEST_TXT.to_string(),
                    TEST_ITEM: first_item.TEST_ITEM.to_string(),
                    HI_SPEC: first_item.HI_SPEC.unwrap_or(0.0),
                    LO_SPEC: first_item.LO_SPEC.unwrap_or(0.0),
                    HI_LIMIT: first_item.HI_LIMIT.unwrap_or(0.0),
                    LO_LIMIT: first_item.LO_LIMIT.unwrap_or(0.0),
                    FIRST_PASS_CNT: sub_index.first_pass_cnt,
                    FINAL_PASS_CNT: sub_index.final_pass_cnt,
                    FIRST_FAIL_CNT: sub_index.first_fail_cnt,
                    FINAL_FAIL_CNT: sub_index.final_fail_cnt,
                    TOTAL_CNT: sub_index.total_cnt,
                    FIRST_MEAN: sub_index.first_mean,
                    FINAL_MEAN: sub_index.final_mean,
                    FIRST_SUM: sub_index.first_sum,
                    FINAL_SUM: sub_index.final_sum,
                    FIRST_STANDARD_SQUARE_SUM: sub_index.first_standard_square_sum,
                    FINAL_STANDARD_SQUARE_SUM: sub_index.final_standard_square_sum,
                    CREATE_HOUR_KEY: create_hour_key,
                    CREATE_DAY_KEY: create_day_key,
                    CREATE_TIME: now,
                    CREATE_USER: SYSTEM.to_string(),
                    VERSION: 1,
                    PROCESS: first_item.PROCESS.to_string(),
                    UPLOAD_TIME: first_item.UPLOAD_TIME.unwrap_or(0),
                };
                
                result.push(bin_test_item_index);
            }
        }
        
        result
    }

    /// 计算final bin,FT需要考虑RETEST_BIN_NUM
    /// 对应Scala中的calculateFinalBinTestItems方法
    fn calculate_final_bin_test_items<'a>(
        &self,
        items: &'a [DwsSubTestItemDetail],
        is_wafer: bool,
        retest_bin_num_map: Option<&HashMap<String, i32>>,
    ) -> Vec<&'a DwsSubTestItemDetail> {
        if items.is_empty() {
            return Vec::new();
        }

        let head = &items[0];
        
        if is_wafer {
            // CP
            if head.HBIN_PF == PF_PASS.into() { // PF_PASS
                items.iter().collect()
            } else {
                items
                    .iter()
                    .filter(|item| item.IS_FINAL_TEST.unwrap_or(0) == 1)
                    .collect()
            }
        } else {
            // FT复测过的TEST_ITEM
            let current_bin_retest: HashMap<String, i32> = if let Some(retest_map) = retest_bin_num_map {
                retest_map
                    .iter()
                    .filter(|(key, _)| {
                        key.is_empty() || 
                        *key == ALL ||
                        key.split(',').any(|k| k == head.HBIN_NUM.unwrap_or(0).to_string())
                    })
                    .map(|(k, v)| (k.clone(), *v))
                    .collect()
            } else {
                HashMap::new()
            };

            if head.HBIN_PF == PF_PASS.into() || current_bin_retest.is_empty() { // PF_PASS
                items.iter().collect()
            } else {
                let bin_max_offline_retest = current_bin_retest.values().max().unwrap_or(&0);
                items
                    .iter()
                    .filter(|item| item.OFFLINE_RETEST.unwrap_or(0) >= *bin_max_offline_retest)
                    .collect()
            }
        }
    }




}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_calculate_bin_test_item_index() {
        use chrono::Utc;
        
        let service = BinTestItemIndexService::new("CP".to_string());
        
        // 创建测试数据
        let test_items = vec![
            DwsSubTestItemDetail {
                IS_FIRST_TEST: Some(1),
                TEST_RESULT: Some(1),
                TEST_VALUE: Some(1.5),
                TESTER_NAME: "TESTER1".to_string(),
                TESTER_TYPE: "TYPE1".to_string(),
                PROBER_HANDLER_ID: "HANDLER1".to_string(),
                PROBECARD_LOADBOARD_ID: "LOADBOARD1".to_string(),
                START_TIME: Some(Utc::now()),
                END_TIME: Some(Utc::now()),
                HBIN_PF: "P".to_string(),
                IS_FINAL_TEST: Some(1),
                OFFLINE_RETEST: Some(0),
                ..Default::default()
            },
            DwsSubTestItemDetail {
                IS_FIRST_TEST: Some(1),
                TEST_RESULT: Some(0),
                TEST_VALUE: Some(2.5),
                TESTER_NAME: "TESTER2".to_string(),
                TESTER_TYPE: "TYPE2".to_string(),
                PROBER_HANDLER_ID: "HANDLER2".to_string(),
                PROBECARD_LOADBOARD_ID: "LOADBOARD2".to_string(),
                START_TIME: Some(Utc::now()),
                END_TIME: Some(Utc::now()),
                HBIN_PF: "F".to_string(),
                IS_FINAL_TEST: Some(1),
                OFFLINE_RETEST: Some(0),
                ..Default::default()
            },
        ];

        let result = service.calculate_bin_test_item_index(&test_items, true, None);
        
        assert_eq!(result.first_pass_cnt, 1);
        assert_eq!(result.first_fail_cnt, 1);
        assert_eq!(result.total_cnt, 2);
        assert!(result.first_mean.is_some());
        assert_eq!(result.first_mean.unwrap(), 2.0); // (1.5 + 2.5) / 2
    }

    #[test]
    fn test_mk_string_distinct() {
        let test1 = "test1".to_string();
        let test2 = "test2".to_string();
        let test3 = "test3".to_string();
        let empty = "".to_string();
        
        let strings = vec![
            &test1,
            &test2,
            &test1,
            &empty,
            &test3,
        ];
        
        let result = DwsService::mk_string_distinct(&strings);
        assert_eq!(result, "test1,test2,test3");
    }
}

// 为DwsSubTestItemDetail实现Default trait以便测试
impl Default for DwsSubTestItemDetail {
    fn default() -> Self {
        Self {
            FILE_ID: None,
            ONLINE_RETEST: None,
            MAX_OFFLINE_RETEST: None,
            MAX_ONLINE_RETEST: None,
            IS_DIE_FIRST_TEST: None,
            IS_DIE_FINAL_TEST: None,
            IS_FIRST_TEST: None,
            IS_FINAL_TEST: None,
            IS_FIRST_TEST_IGNORE_TP: None,
            IS_FINAL_TEST_IGNORE_TP: None,
            IS_DUP_FIRST_TEST: None,
            IS_DUP_FINAL_TEST: None,
            IS_DUP_FIRST_TEST_IGNORE_TP: None,
            IS_DUP_FINAL_TEST_IGNORE_TP: None,
            TEST_SUITE: Arc::from(""),
            CONDITION_SET: None,
            TEST_NUM: None,
            TEST_TXT: String::new().into(),
            TEST_ITEM: String::new().into(),
            IS_DIE_FIRST_TEST_ITEM: None,
            TESTITEM_TYPE: String::new().into(),
            TEST_FLG: Arc::from(""),
            PARM_FLG: Arc::from(""),
            TEST_STATE: Arc::from(""),
            TEST_VALUE: None,
            UNITS: String::new().into(),
            TEST_RESULT: None,
            ORIGIN_TEST_VALUE: None,
            ORIGIN_UNITS: String::new().into(),
            TEST_ORDER: None,
            ALARM_ID: Arc::from(""),
            OPT_FLG: Arc::from(""),
            RES_SCAL: None,
            NUM_TEST: None,
            LLM_SCAL: None,
            HLM_SCAL: None,
            LO_LIMIT: None,
            HI_LIMIT: None,
            ORIGIN_HI_LIMIT: None,
            ORIGIN_LO_LIMIT: None,
            C_RESFMT: Arc::from(""),
            C_LLMFMT: Arc::from(""),
            C_HLMFMT: Arc::from(""),
            LO_SPEC: None,
            HI_SPEC: None,
            HBIN_NUM: None,
            SBIN_NUM: None,
            SBIN_PF: String::new().into(),
            SBIN_NAM: String::new().into(),
            HBIN_PF: String::new().into(),
            HBIN_NAM: String::new().into(),
            HBIN: String::new().into(),
            SBIN: String::new().into(),
            TEST_HEAD: None,
            PART_FLG: Arc::from(""),
            PART_ID: String::new().into(),
            C_PART_ID: None,
            ECID: String::new().into(),
            ECID_EXT: Arc::from(""),
            ECID_EXTRA: None,
            IS_STANDARD_ECID: None,
            X_COORD: None,
            Y_COORD: None,
            DIE_X: None,
            DIE_Y: None,
            TEST_TIME: None,
            PART_TXT: Arc::from(""),
            PART_FIX: Arc::from(""),
            SITE: None,
            TOUCH_DOWN_ID: None,
            WAFER_LOT_ID: Arc::from(""),
            WAFER_ID: String::new().into(),
            WAFER_NO: String::new().into(),
            RETICLE_T_X: None,
            RETICLE_T_Y: None,
            RETICLE_X: None,
            RETICLE_Y: None,
            SITE_ID: Arc::from(""),
            VECT_NAM: Arc::from(""),
            TIME_SET: Arc::from(""),
            NUM_FAIL: None,
            FAIL_PIN: Arc::from(""),
            CYCL_CNT: None,
            REPT_CNT: None,
            LONG_ATTRIBUTE_SET: None,
            STRING_ATTRIBUTE_SET: None,
            FLOAT_ATTRIBUTE_SET: None,
            UID: Arc::from(""),
            TEXT_DAT: Arc::from(""),
            CREATE_HOUR_KEY: Arc::from(""),
            CREATE_DAY_KEY: Arc::from(""),
            CREATE_TIME: Some(0),
            EFUSE_EXTRA: None,
            CHIP_ID: Arc::from(""),
            CUSTOMER: String::new().into(),
            SUB_CUSTOMER: String::new().into(),
            UPLOAD_TYPE: String::new().into(),
            FILE_NAME: String::new().into(),
            FILE_TYPE: String::new().into(),
            FACTORY: String::new().into(),
            FACTORY_SITE: String::new().into(),
            FAB: String::new().into(),
            FAB_SITE: String::new().into(),
            LOT_TYPE: String::new().into(),
            TEST_AREA: String::new().into(),
            OFFLINE_RETEST: None,
            INTERRUPT: None,
            DUP_RETEST: None,
            BATCH_NUM: None,
            LOT_ID: String::new().into(),
            SBLOT_ID: String::new().into(),
            PROBER_HANDLER_ID: String::new().into(),
            TESTER_TYPE: String::new().into(),
            TEST_STAGE: String::new().into(),
            DEVICE_ID: String::new().into(),
            TEST_PROGRAM: String::new().into(),
            TEST_TEMPERATURE: String::new().into(),
            TEST_PROGRAM_VERSION: String::new().into(),
            TESTER_NAME: String::new().into(),
            OPERATOR_NAME: String::new().into(),
            PROBECARD_LOADBOARD_ID: String::new().into(),
            START_TIME: None,
            END_TIME: None,
            START_HOUR_KEY: String::new().into(),
            START_DAY_KEY: String::new().into(),
            END_HOUR_KEY: String::new().into(),
            END_DAY_KEY: String::new().into(),
            FLOW_ID: String::new().into(),
            RETEST_BIN_NUM: String::new().into(),
            PROCESS: String::new().into(),
            UPLOAD_TIME: Some(0),
        }
}
}
