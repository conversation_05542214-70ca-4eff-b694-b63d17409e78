use std::collections::HashMap;
use std::hash::{Hash, <PERSON><PERSON>};
use std::cmp::Ordering;

// ------------------ 数据结构定义 ------------------

#[derive(Debug, <PERSON>lone, Eq)]
pub struct Sblot {
    pub customer: String,
    pub factory: String,
    pub device_id: String,
    pub test_area: String,
    pub lot_id: String,
    pub lot_type: String,
    pub sblot_id: String,
    pub test_stage: String,
    pub test_program: String,
}

#[derive(Debug, <PERSON><PERSON>, Eq)]
pub struct Lot {
    pub customer: String,
    pub factory: String,
    pub device_id: String,
    pub test_area: String,
    pub lot_type: String,
    pub lot_id: String,
    pub test_stage: String,
    pub test_program: String,
}

impl From<&Sblot> for Lot {
    fn from(sb: &Sblot) -> Self {
        Lot {
            customer: sb.customer.clone(),
            factory: sb.factory.clone(),
            device_id: sb.device_id.clone(),
            test_area: sb.test_area.clone(),
            lot_type: sb.lot_type.clone(),
            lot_id: sb.lot_id.clone(),
            test_stage: sb.test_stage.clone(),
            test_program: sb.test_program.clone(),
            // 使用结构体更新语法，如果后续字段增加，可以简化代码
            // ..*sb  // 如果 Sblot 的字段与 Lot 完全一致，可以使用这种方式
        }
    }
}

// 为 HashMap 做 Hash / PartialEq
impl Hash for Sblot {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.customer.hash(state);
        self.factory.hash(state);
        self.device_id.hash(state);
        self.test_area.hash(state);
        self.lot_id.hash(state);
        self.lot_type.hash(state);
        self.sblot_id.hash(state);
        self.test_stage.hash(state);
        self.test_program.hash(state);
    }
}

impl PartialEq for Sblot {
    fn eq(&self, other: &Self) -> bool {
        self.customer == other.customer
            && self.factory == other.factory
            && self.device_id == other.device_id
            && self.test_area == other.test_area
            && self.lot_id == other.lot_id
            && self.lot_type == other.lot_type
            && self.sblot_id == other.sblot_id
            && self.test_stage == other.test_stage
            && self.test_program == other.test_program
    }
}

impl Ord for Sblot {
    fn cmp(&self, other: &Self) -> Ordering {
        self.customer
            .cmp(&other.customer)
            .then_with(|| self.factory.cmp(&other.factory))
            .then_with(|| self.device_id.cmp(&other.device_id))
            .then_with(|| self.test_area.cmp(&other.test_area))
            .then_with(|| self.lot_id.cmp(&other.lot_id))
            .then_with(|| self.lot_type.cmp(&other.lot_type))
            .then_with(|| self.sblot_id.cmp(&other.sblot_id))
            .then_with(|| self.test_stage.cmp(&other.test_stage))
            .then_with(|| self.test_program.cmp(&other.test_program))
    }
}

impl PartialOrd for Sblot {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some(self.cmp(other))
    }
}

impl Hash for Lot {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.customer.hash(state);
        self.factory.hash(state);
        self.device_id.hash(state);
        self.test_area.hash(state);
        self.lot_type.hash(state);
        self.lot_id.hash(state);
        self.test_stage.hash(state);
        self.test_program.hash(state);
    }
}

impl PartialEq for Lot {
    fn eq(&self, other: &Self) -> bool {
        self.customer == other.customer
            && self.factory == other.factory
            && self.device_id == other.device_id
            && self.test_area == other.test_area
            && self.lot_type == other.lot_type
            && self.lot_id == other.lot_id
            && self.test_stage == other.test_stage
            && self.test_program == other.test_program
    }
}

impl Ord for Lot {
    fn cmp(&self, other: &Self) -> Ordering {
        self.customer
            .cmp(&other.customer)
            .then_with(|| self.factory.cmp(&other.factory))
            .then_with(|| self.device_id.cmp(&other.device_id))
            .then_with(|| self.test_area.cmp(&other.test_area))
            .then_with(|| self.lot_type.cmp(&other.lot_type))
            .then_with(|| self.lot_id.cmp(&other.lot_id))
            .then_with(|| self.test_stage.cmp(&other.test_stage))
            .then_with(|| self.test_program.cmp(&other.test_program))
    }
}

impl PartialOrd for Lot {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some(self.cmp(other))
    }
}

pub type Bin = String;
pub type Count = i32;

// ------------------ 聚合函数 ------------------

/// 将sblot维度的数据聚合到lot维度
/// 对应Scala中的: sblotRetestBinNumMap.groupBy { case (sblot, _) => Lot(...) }.mapValues(...)
pub fn aggregate_sblot_to_lot(
    input: HashMap<Sblot, HashMap<Bin, Count>>,
) -> HashMap<Lot, HashMap<Bin, Count>> {
    let mut lot_map: HashMap<Lot, HashMap<Bin, Count>> = HashMap::new();

    for (sblot, bin_map) in input {
        // 转换 sblot 为 lot 维度
        let lot = Lot::from(&sblot);

        // 获取当前 lot 对应的 HashMap，如果不存在则插入一个新的
        let lot_entry = lot_map.entry(lot).or_insert_with(HashMap::new);

        // 处理每个 bin-count 对
        for (bin, cnt) in bin_map {
            // 对于已存在的 bin，保留最大值；不存在则直接插入
            match lot_entry.get(&bin) {
                Some(existing_cnt) => {
                    if cnt > *existing_cnt {
                        lot_entry.insert(bin, cnt);
                    }
                }
                None => {
                    lot_entry.insert(bin, cnt);
                }
            }
        }
    }

    lot_map
}

/// 从DwsSubTestItemDetail构建Sblot
impl Sblot {
    pub fn from_test_item_detail(item: &crate::dws::dws_service::DwsSubTestItemDetail) -> Self {
        Sblot {
            customer: item.CUSTOMER.to_string(),
            factory: item.FACTORY.to_string(),
            device_id: item.DEVICE_ID.to_string(),
            test_area: item.TEST_AREA.to_string(),
            lot_id: item.LOT_ID.to_string(),
            lot_type: item.LOT_TYPE.to_string(),
            sblot_id: item.SBLOT_ID.to_string(),
            test_stage: item.TEST_STAGE.to_string(),
            test_program: item.TEST_PROGRAM.to_string(),
        }
    }
}

/// 从DwsSubTestItemDetail构建Lot
impl Lot {
    pub fn from_test_item_detail(item: &crate::dws::dws_service::DwsSubTestItemDetail) -> Self {
        Lot {
            customer: item.CUSTOMER.to_string(),
            factory: item.FACTORY.to_string(),
            device_id: item.DEVICE_ID.to_string(),
            test_area: item.TEST_AREA.to_string(),
            lot_type: item.LOT_TYPE.to_string(),
            lot_id: item.LOT_ID.to_string(),
            test_stage: item.TEST_STAGE.to_string(),
            test_program: item.TEST_PROGRAM.to_string(),
        }
    }
}

// ------------------ 测试 ------------------

#[cfg(test)]
mod tests {
    use super::*;

    fn make_sblot(id: u8) -> Sblot {
        Sblot {
            customer: format!("cust-{}", id),
            factory: format!("fab-{}", id),
            device_id: format!("dev-{}", id),
            test_area: format!("area-{}", id),
            lot_id: format!("lot-{}", id),
            lot_type: format!("type-{}", id),
            sblot_id: format!("sblot-{}", id),
            test_stage: format!("stage-{}", id),
            test_program: format!("prog-{}", id),
        }
    }

    #[test]
    fn test_aggregate_sblot_to_lot() {
        let mut input = HashMap::new();

        // 第一个sblot
        let s1 = Sblot {
            customer: "cust-1".to_string(),
            factory: "fab-1".to_string(),
            device_id: "dev-1".to_string(),
            test_area: "area-1".to_string(),
            lot_id: "lot-1".to_string(),
            lot_type: "type-1".to_string(),
            sblot_id: "sblot-1".to_string(),
            test_stage: "stage-1".to_string(),
            test_program: "prog-1".to_string(),
        };
        let mut bin_map1 = HashMap::new();
        bin_map1.insert("bin1".to_string(), 10);
        bin_map1.insert("bin2".to_string(), 20);
        input.insert(s1.clone(), bin_map1);

        // 第二个sblot，与第一个属于同一个lot
        let s2 = Sblot {
            customer: "cust-1".to_string(),
            factory: "fab-1".to_string(),
            device_id: "dev-1".to_string(),
            test_area: "area-1".to_string(),
            lot_id: "lot-1".to_string(),
            lot_type: "type-1".to_string(),
            sblot_id: "sblot-2".to_string(),
            test_stage: "stage-1".to_string(),
            test_program: "prog-1".to_string(),
        };
        let mut bin_map2 = HashMap::new();
        bin_map2.insert("bin2".to_string(), 5);   // 同 bin 取 max(20,5) = 20
        bin_map2.insert("bin3".to_string(), 99);  // 新 bin
        input.insert(s2, bin_map2);

        let result = aggregate_sblot_to_lot(input);

        // 调试信息
        println!("Result map has {} entries", result.len());
        for (lot, bins) in &result {
            println!("Lot: {:?}", lot);
            println!("Bins: {:?}", bins);
        }

        assert_eq!(result.len(), 1);
        // 使用from方法从Sblot创建Lot对象以确保一致性
        let lot_key = Lot::from(&s1);
        let bins = result.get(&lot_key).expect("Lot should exist in result");

        // 检查聚合结果
        assert_eq!(*bins.get(&"bin1".to_string()).expect("bin1 should exist"), 10);
        assert_eq!(*bins.get(&"bin2".to_string()).expect("bin2 should exist"), 20); // 取最大值
        assert_eq!(*bins.get(&"bin3".to_string()).expect("bin3 should exist"), 99);
    }

    #[test]
    fn test_different_lots() {
        let mut input = HashMap::new();

        // 第一个lot
        let s1 = make_sblot(1);
        let mut bin_map1 = HashMap::new();
        bin_map1.insert("bin1".to_string(), 10);
        input.insert(s1.clone(), bin_map1);

        // 第二个lot（不同的lot_id）
        let s2 = Sblot {
            lot_id: "lot-2".to_string(), // 不同的lot
            ..make_sblot(1)
        };
        let mut bin_map2 = HashMap::new();
        bin_map2.insert("bin1".to_string(), 5);
        input.insert(s2.clone(), bin_map2);

        let result = aggregate_sblot_to_lot(input);

        // 应该有两个不同的lot
        assert_eq!(result.len(), 2);

        let lot1 = Lot::from(&s1);
        let lot2 = Lot::from(&s2);

        assert_eq!(result[&lot1][&"bin1".to_string()], 10);
        assert_eq!(result[&lot2][&"bin1".to_string()], 5);
    }
}