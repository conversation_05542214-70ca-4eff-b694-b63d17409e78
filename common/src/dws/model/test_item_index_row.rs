use crate::utils::decimal::Decimal38_18;
use chrono::{DateTime, Utc};
use clickhouse::Row;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use super::test_item::TestItemIndex;

/// 测试项索引ClickHouse行数据结构
/// 对应ClickHouse表的数据结构，使用与dwd相同的字段类型
#[derive(Row, Serialize, Deserialize, Debug, PartialEq, Clone)]
#[allow(non_snake_case)]
pub struct TestItemIndexRow {
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub LOT_ID: Arc<str>,
    pub PROCESS: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub WAFER_LOT_ID: Arc<str>,
    pub WAFER_ID: Arc<str>,
    pub WAFER_ID_KEY: Arc<str>,
    pub WAFER_NO: Arc<str>,
    pub WAFER_NO_KEY: Arc<str>,
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub OFFLINE_RETEST: Option<u8>,
    pub INTERRUPT: Option<u8>,
    pub DUP_RETEST: Option<u8>,
    pub BATCH_NUM: Option<u8>,
    pub LO_LIMIT: Option<Decimal38_18>,
    pub HI_LIMIT: Option<Decimal38_18>,
    pub ORIGIN_HI_LIMIT: Option<Decimal38_18>,
    pub ORIGIN_LO_LIMIT: Option<Decimal38_18>,
    pub FILE_ID: u32,
    pub FILE_NAME: Arc<str>,
    pub FILE_TYPE: Arc<str>,
    pub TESTER_NAME: Arc<str>,
    pub TESTER_TYPE: Arc<str>,
    pub PROBER_HANDLER_ID: Arc<str>,
    pub PROBECARD_LOADBOARD_ID: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub START_TIME: Option<DateTime<Utc>>,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub END_TIME: Option<DateTime<Utc>>,
    pub START_HOUR_KEY: Arc<str>,
    pub START_DAY_KEY: Arc<str>,
    pub END_HOUR_KEY: Arc<str>,
    pub END_DAY_KEY: Arc<str>,
    pub FLOW_ID: Arc<str>,
    pub FINAL_FLAG: u8,
    pub BIN_PF: Arc<str>,
    pub TESTITEM_TYPE: Arc<str>,
    pub TEST_NUM: Option<u32>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,
    pub UNITS: Arc<str>,
    pub ORIGIN_UNITS: Arc<str>,
    pub MEDIAN: Option<Decimal38_18>,
    pub MEAN: Option<Decimal38_18>,
    pub MAX: Option<Decimal38_18>,
    pub MIN: Option<Decimal38_18>,
    pub STANDARD_DEVIATION: Option<Decimal38_18>,
    pub RANGE: Option<Decimal38_18>,
    pub IQR: Option<Decimal38_18>,
    pub Q1: Option<Decimal38_18>,
    pub Q3: Option<Decimal38_18>,
    pub CNT: Option<u32>,
    pub FAIL_CNT: Option<u32>,
    pub FAIL_RATE: Option<Decimal38_18>,
    pub CP: Option<Decimal38_18>,
    pub CPK: Option<Decimal38_18>,
    pub RETEST_BIN_NUM: Arc<str>,
    pub CREATE_HOUR_KEY: Arc<str>,
    pub CREATE_DAY_KEY: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub CREATE_TIME: DateTime<Utc>,
    pub CREATE_USER: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub UPLOAD_TIME: DateTime<Utc>,
    pub VERSION: i64,
    pub IS_DELETE: u8,
}

impl TestItemIndexRow {
    /// 从HDFS实体类转换为ClickHouse实体类
    pub fn from_hdfs_entity(hdfs_entity: &TestItemIndex) -> Self {
        use crate::utils::decimal::IntoDecimal38_18;
        
        Self {
            CUSTOMER: Arc::from(hdfs_entity.CUSTOMER.as_str()),
            SUB_CUSTOMER: Arc::from(hdfs_entity.SUB_CUSTOMER.as_str()),
            UPLOAD_TYPE: Arc::from(hdfs_entity.UPLOAD_TYPE.as_str()),
            FACTORY: Arc::from(hdfs_entity.FACTORY.as_str()),
            FACTORY_SITE: Arc::from(hdfs_entity.FACTORY_SITE.as_str()),
            FAB: Arc::from(hdfs_entity.FAB.as_str()),
            FAB_SITE: Arc::from(hdfs_entity.FAB_SITE.as_str()),
            TEST_AREA: Arc::from(hdfs_entity.TEST_AREA.as_str()),
            TEST_STAGE: Arc::from(hdfs_entity.TEST_STAGE.as_str()),
            LOT_TYPE: Arc::from(hdfs_entity.LOT_TYPE.as_str()),
            DEVICE_ID: Arc::from(hdfs_entity.DEVICE_ID.as_str()),
            LOT_ID: Arc::from(hdfs_entity.LOT_ID.as_str()),
            PROCESS: Arc::from(hdfs_entity.PROCESS.as_str()),
            SBLOT_ID: Arc::from(hdfs_entity.SBLOT_ID.as_str()),
            WAFER_LOT_ID: Arc::from(hdfs_entity.WAFER_LOT_ID.as_str()),
            WAFER_ID: Arc::from(hdfs_entity.WAFER_ID.as_str()),
            WAFER_ID_KEY: Arc::from(hdfs_entity.WAFER_ID_KEY.as_str()),
            WAFER_NO: Arc::from(hdfs_entity.WAFER_NO.as_str()),
            WAFER_NO_KEY: Arc::from(hdfs_entity.WAFER_NO_KEY.as_str()),
            TEST_PROGRAM: Arc::from(hdfs_entity.TEST_PROGRAM.as_str()),
            TEST_TEMPERATURE: Arc::from(hdfs_entity.TEST_TEMPERATURE.as_str()),
            TEST_PROGRAM_VERSION: Arc::from(hdfs_entity.TEST_PROGRAM_VERSION.as_str()),
            OFFLINE_RETEST: hdfs_entity.OFFLINE_RETEST.map(|v| v as u8),
            INTERRUPT: hdfs_entity.INTERRUPT.map(|v| v as u8),
            DUP_RETEST: hdfs_entity.DUP_RETEST.map(|v| v as u8),
            BATCH_NUM: hdfs_entity.BATCH_NUM.map(|v| v as u8),
            LO_LIMIT: Some(hdfs_entity.LO_LIMIT.into_decimal38_18()),
            HI_LIMIT: Some(hdfs_entity.HI_LIMIT.into_decimal38_18()),
            ORIGIN_HI_LIMIT: Some(hdfs_entity.ORIGIN_HI_LIMIT.into_decimal38_18()),
            ORIGIN_LO_LIMIT: Some(hdfs_entity.ORIGIN_LO_LIMIT.into_decimal38_18()),
            FILE_ID: hdfs_entity.FILE_ID as u32,
            FILE_NAME: Arc::from(hdfs_entity.FILE_NAME.as_str()),
            FILE_TYPE: Arc::from(hdfs_entity.FILE_TYPE.as_str()),
            TESTER_NAME: Arc::from(hdfs_entity.TESTER_NAME.as_str()),
            TESTER_TYPE: Arc::from(hdfs_entity.TESTER_TYPE.as_str()),
            PROBER_HANDLER_ID: Arc::from(hdfs_entity.PROBER_HANDLER_ID.as_str()),
            PROBECARD_LOADBOARD_ID: Arc::from(hdfs_entity.PROBECARD_LOADBOARD_ID.as_str()),
            START_TIME: chrono::DateTime::from_timestamp_millis(hdfs_entity.START_TIME),
            END_TIME: chrono::DateTime::from_timestamp_millis(hdfs_entity.END_TIME),
            START_HOUR_KEY: Arc::from(hdfs_entity.START_HOUR_KEY.as_str()),
            START_DAY_KEY: Arc::from(hdfs_entity.START_DAY_KEY.as_str()),
            END_HOUR_KEY: Arc::from(hdfs_entity.END_HOUR_KEY.as_str()),
            END_DAY_KEY: Arc::from(hdfs_entity.END_DAY_KEY.as_str()),
            FLOW_ID: Arc::from(hdfs_entity.FLOW_ID.as_str()),
            FINAL_FLAG: hdfs_entity.FINAL_FLAG as u8,
            BIN_PF: Arc::from(hdfs_entity.BIN_PF.as_str()),
            TESTITEM_TYPE: Arc::from(hdfs_entity.TESTITEM_TYPE.as_str()),
            TEST_NUM: Some(hdfs_entity.TEST_NUM as u32),
            TEST_TXT: Arc::from(hdfs_entity.TEST_TXT.as_str()),
            TEST_ITEM: Arc::from(hdfs_entity.TEST_ITEM.as_str()),
            UNITS: Arc::from(hdfs_entity.UNITS.as_str()),
            ORIGIN_UNITS: Arc::from(hdfs_entity.ORIGIN_UNITS.as_str()),
            MEDIAN: Some(hdfs_entity.MEDIAN.into_decimal38_18()),
            MEAN: Some(hdfs_entity.MEAN.into_decimal38_18()),
            MAX: Some(hdfs_entity.MAX.into_decimal38_18()),
            MIN: Some(hdfs_entity.MIN.into_decimal38_18()),
            STANDARD_DEVIATION: Some(hdfs_entity.STANDARD_DEVIATION.into_decimal38_18()),
            RANGE: Some(hdfs_entity.RANGE.into_decimal38_18()),
            IQR: Some(hdfs_entity.IQR.into_decimal38_18()),
            Q1: Some(hdfs_entity.Q1.into_decimal38_18()),
            Q3: Some(hdfs_entity.Q3.into_decimal38_18()),
            CNT: Some(hdfs_entity.CNT as u32),
            FAIL_CNT: Some(hdfs_entity.FAIL_CNT as u32),
            FAIL_RATE: Some(hdfs_entity.FAIL_RATE.into_decimal38_18()),
            CP: Some(hdfs_entity.CP.into_decimal38_18()),
            CPK: Some(hdfs_entity.CPK.into_decimal38_18()),
            RETEST_BIN_NUM: Arc::from(hdfs_entity.RETEST_BIN_NUM.as_str()),
            CREATE_HOUR_KEY: Arc::from(hdfs_entity.CREATE_HOUR_KEY.as_str()),
            CREATE_DAY_KEY: Arc::from(hdfs_entity.CREATE_DAY_KEY.as_str()),
            CREATE_TIME: chrono::DateTime::from_timestamp_millis(hdfs_entity.CREATE_TIME).unwrap_or_else(|| chrono::Utc::now()),
            CREATE_USER: Arc::from(hdfs_entity.CREATE_USER.as_str()),
            UPLOAD_TIME: chrono::DateTime::from_timestamp_millis(hdfs_entity.UPLOAD_TIME).unwrap_or_else(|| chrono::Utc::now()),
            VERSION: hdfs_entity.VERSION,
            IS_DELETE: 0,
        }
    }
}
