use sqlx::FromRow;

#[derive(Debug, Clone, FromRow)]
pub struct DwTableCalculateRecord {
    pub customer: Option<String>,
    pub sub_customer: Option<String>,
    pub upload_type: Option<String>,
    pub file_id: Option<i64>,
    pub file_name: Option<String>,
    pub test_area: Option<String>,
    pub factory: Option<String>,
    pub factory_site: Option<String>,
    pub device_id: Option<String>,
    pub lot_id: Option<String>,
    pub wafer_no: Option<String>,
    pub file_category: Option<String>,
    pub lot_type: Option<String>,
    pub test_stage: Option<String>,
    pub dw_layer: Option<String>,
    pub db: Option<String>,
    pub local_name: Option<String>,
    pub cluster_name: Option<String>,
    pub calculate_start_time: Option<i64>,
    pub calculate_end_time: Option<i64>,
    pub calculate_time_diff: Option<i64>,
    pub sink_ck_start_time: Option<i64>,
    pub sink_ck_end_time: Option<i64>,
    pub sink_ck_time_diff: Option<i64>,
    pub warehousing_mode: Option<String>,
    pub file_size: Option<i64>,
    pub die_data_count: Option<i32>,
    pub test_item_data_count: Option<i64>,
    pub run_mode: Option<String>,
    pub layer_calculate_pool_id: Option<i64>,
    pub num_executors: Option<i32>,
    pub executor_cores: Option<i32>,
    pub executor_memory: Option<i32>,
    pub driver_memory: Option<i32>,
    pub parallelism: Option<i32>,
    pub extra_conf: Option<String>,
}