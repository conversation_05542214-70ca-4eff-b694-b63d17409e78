use sqlx::FromRow;

#[derive(Debug, <PERSON><PERSON>, FromRow)]
pub struct DwLayerCalculatePool {
    pub layer_calculate_pool_id: Option<i64>,
    pub run_mode: Option<String>,
    pub num_executors: Option<i32>,
    pub executor_cores: Option<i32>,
    pub executor_memory: Option<i32>,
    pub driver_memory: Option<i32>,
    pub parallelism: Option<i32>,
    pub extra_conf: Option<String>,
    pub warehousing_mode: Option<String>,
}