use super::bin_test_item::BinTestItemIndex;
use crate::utils::decimal::Decimal38_18;
use chrono::{DateTime, Utc};
use clickhouse::Row;
use serde::{Deserialize, Serialize};
use std::sync::Arc;

/// Bin测试项索引ClickHouse行数据结构
/// 对应ClickHouse表的数据结构，使用与dwd相同的字段类型
#[derive(Row, Serialize, Deserialize, Debug, PartialEq, Clone)]
#[allow(non_snake_case)]
pub struct BinTestItemIndexRow {
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub FILE_ID: u32,
    pub FILE_NAME: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub LOT_ID: Arc<str>,
    pub PROCESS: Arc<str>,
    pub WAFER_ID: Arc<str>,
    pub WAFER_ID_KEY: Arc<str>,
    pub WAFER_NO: Arc<str>,
    pub WAFER_NO_KEY: Arc<str>,
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,
    pub TESTER_NAME: Arc<str>,
    pub TESTER_TYPE: Arc<str>,
    pub PROBER_HANDLER_ID: Arc<str>,
    pub PROBECARD_LOADBOARD_ID: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub START_TIME: Option<DateTime<Utc>>,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub END_TIME: Option<DateTime<Utc>>,
    pub START_HOUR_KEY: Arc<str>,
    pub START_DAY_KEY: Arc<str>,
    pub END_HOUR_KEY: Arc<str>,
    pub END_DAY_KEY: Arc<str>,
    pub HBIN_NUM: Option<u32>,
    pub HBIN_NUM_KEY: Arc<str>,
    pub SBIN_NUM: Option<u32>,
    pub SBIN_NUM_KEY: Arc<str>,
    pub SBIN_PF: Arc<str>,
    pub SBIN_NAM: Arc<str>,
    pub HBIN_PF: Arc<str>,
    pub HBIN_NAM: Arc<str>,
    pub HBIN: Arc<str>,
    pub SBIN: Arc<str>,
    pub TESTITEM_TYPE: Arc<str>,
    pub TEST_NUM: Option<u32>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,
    pub HI_SPEC: Option<Decimal38_18>,
    pub LO_SPEC: Option<Decimal38_18>,
    pub LO_LIMIT: Option<Decimal38_18>,
    pub HI_LIMIT: Option<Decimal38_18>,
    pub FIRST_PASS_CNT: Option<u32>,
    pub FINAL_PASS_CNT: Option<u32>,
    pub FIRST_FAIL_CNT: Option<u32>,
    pub FINAL_FAIL_CNT: Option<u32>,
    pub TOTAL_CNT: Option<u32>,
    pub FIRST_MEAN: Option<Decimal38_18>,
    pub FINAL_MEAN: Option<Decimal38_18>,
    pub FIRST_SUM: Option<Decimal38_18>,
    pub FINAL_SUM: Option<Decimal38_18>,
    pub FIRST_STANDARD_SQUARE_SUM: Option<Decimal38_18>,
    pub FINAL_STANDARD_SQUARE_SUM: Option<Decimal38_18>,
    pub CREATE_HOUR_KEY: Arc<str>,
    pub CREATE_DAY_KEY: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub CREATE_TIME: DateTime<Utc>,
    pub CREATE_USER: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub UPLOAD_TIME: DateTime<Utc>,
    pub VERSION: i64,
    pub IS_DELETE: u8,
}

impl BinTestItemIndexRow {
    /// 从HDFS实体类转换为ClickHouse实体类
    pub fn from_hdfs_entity(hdfs_entity: &BinTestItemIndex) -> Self {
        use crate::utils::decimal::IntoDecimal38_18;

        Self {
            CUSTOMER: Arc::from(hdfs_entity.CUSTOMER.as_str()),
            SUB_CUSTOMER: Arc::from(hdfs_entity.SUB_CUSTOMER.as_str()),
            UPLOAD_TYPE: Arc::from(hdfs_entity.UPLOAD_TYPE.as_str()),
            FILE_ID: hdfs_entity.FILE_ID as u32,
            FILE_NAME: Arc::from(hdfs_entity.FILE_NAME.as_str()),
            FACTORY: Arc::from(hdfs_entity.FACTORY.as_str()),
            FACTORY_SITE: Arc::from(hdfs_entity.FACTORY_SITE.as_str()),
            FAB: Arc::from(hdfs_entity.FAB.as_str()),
            FAB_SITE: Arc::from(hdfs_entity.FAB_SITE.as_str()),
            TEST_AREA: Arc::from(hdfs_entity.TEST_AREA.as_str()),
            TEST_STAGE: Arc::from(hdfs_entity.TEST_STAGE.as_str()),
            LOT_TYPE: Arc::from(hdfs_entity.LOT_TYPE.as_str()),
            DEVICE_ID: Arc::from(hdfs_entity.DEVICE_ID.as_str()),
            LOT_ID: Arc::from(hdfs_entity.LOT_ID.as_str()),
            PROCESS: Arc::from(hdfs_entity.PROCESS.as_str()),
            WAFER_ID: Arc::from(hdfs_entity.WAFER_ID.as_str()),
            WAFER_ID_KEY: Arc::from(hdfs_entity.WAFER_ID_KEY.as_str()),
            WAFER_NO: Arc::from(hdfs_entity.WAFER_NO.as_str()),
            WAFER_NO_KEY: Arc::from(hdfs_entity.WAFER_NO_KEY.as_str()),
            TEST_PROGRAM: Arc::from(hdfs_entity.TEST_PROGRAM.as_str()),
            TEST_TEMPERATURE: Arc::from(hdfs_entity.TEST_TEMPERATURE.as_str()),
            TESTER_NAME: Arc::from(hdfs_entity.TESTER_NAME.as_str()),
            TESTER_TYPE: Arc::from(hdfs_entity.TESTER_TYPE.as_str()),
            PROBER_HANDLER_ID: Arc::from(hdfs_entity.PROBER_HANDLER_ID.as_str()),
            PROBECARD_LOADBOARD_ID: Arc::from(hdfs_entity.PROBECARD_LOADBOARD_ID.as_str()),
            START_TIME: hdfs_entity
                .START_TIME
                .map(|ts| chrono::DateTime::from_timestamp_millis(ts).unwrap_or_else(|| chrono::Utc::now())),
            END_TIME: hdfs_entity
                .END_TIME
                .map(|ts| chrono::DateTime::from_timestamp_millis(ts).unwrap_or_else(|| chrono::Utc::now())),
            START_HOUR_KEY: Arc::from(hdfs_entity.START_HOUR_KEY.as_str()),
            START_DAY_KEY: Arc::from(hdfs_entity.START_DAY_KEY.as_str()),
            END_HOUR_KEY: Arc::from(hdfs_entity.END_HOUR_KEY.as_str()),
            END_DAY_KEY: Arc::from(hdfs_entity.END_DAY_KEY.as_str()),
            HBIN_NUM: Some(hdfs_entity.HBIN_NUM as u32),
            HBIN_NUM_KEY: Arc::from(hdfs_entity.HBIN_NUM_KEY.as_str()),
            SBIN_NUM: Some(hdfs_entity.SBIN_NUM as u32),
            SBIN_NUM_KEY: Arc::from(hdfs_entity.SBIN_NUM_KEY.as_str()),
            SBIN_PF: Arc::from(hdfs_entity.SBIN_PF.as_str()),
            SBIN_NAM: Arc::from(hdfs_entity.SBIN_NAM.as_str()),
            HBIN_PF: Arc::from(hdfs_entity.HBIN_PF.as_str()),
            HBIN_NAM: Arc::from(hdfs_entity.HBIN_NAM.as_str()),
            HBIN: Arc::from(hdfs_entity.HBIN.as_str()),
            SBIN: Arc::from(hdfs_entity.SBIN.as_str()),
            TESTITEM_TYPE: Arc::from(hdfs_entity.TESTITEM_TYPE.as_str()),
            TEST_NUM: Some(hdfs_entity.TEST_NUM as u32),
            TEST_TXT: Arc::from(hdfs_entity.TEST_TXT.as_str()),
            TEST_ITEM: Arc::from(hdfs_entity.TEST_ITEM.as_str()),
            HI_SPEC: Some(hdfs_entity.HI_SPEC.into_decimal38_18()),
            LO_SPEC: Some(hdfs_entity.LO_SPEC.into_decimal38_18()),
            HI_LIMIT: Some(hdfs_entity.HI_LIMIT.into_decimal38_18()),
            LO_LIMIT: Some(hdfs_entity.LO_LIMIT.into_decimal38_18()),
            FIRST_PASS_CNT: Some(hdfs_entity.FIRST_PASS_CNT as u32),
            FINAL_PASS_CNT: Some(hdfs_entity.FINAL_PASS_CNT as u32),
            FIRST_FAIL_CNT: Some(hdfs_entity.FIRST_FAIL_CNT as u32),
            FINAL_FAIL_CNT: Some(hdfs_entity.FINAL_FAIL_CNT as u32),
            TOTAL_CNT: Some(hdfs_entity.TOTAL_CNT as u32),
            FIRST_MEAN: hdfs_entity.FIRST_MEAN.map(|v| v.into_decimal38_18()),
            FINAL_MEAN: hdfs_entity.FINAL_MEAN.map(|v| v.into_decimal38_18()),
            FIRST_SUM: hdfs_entity.FIRST_SUM.map(|v| v.into_decimal38_18()),
            FINAL_SUM: hdfs_entity.FINAL_SUM.map(|v| v.into_decimal38_18()),
            FIRST_STANDARD_SQUARE_SUM: hdfs_entity.FIRST_STANDARD_SQUARE_SUM.map(|v| v.into_decimal38_18()),
            FINAL_STANDARD_SQUARE_SUM: hdfs_entity.FINAL_STANDARD_SQUARE_SUM.map(|v| v.into_decimal38_18()),
            CREATE_HOUR_KEY: Arc::from(hdfs_entity.CREATE_HOUR_KEY.as_str()),
            CREATE_DAY_KEY: Arc::from(hdfs_entity.CREATE_DAY_KEY.as_str()),
            CREATE_TIME: chrono::DateTime::from_timestamp_millis(hdfs_entity.CREATE_TIME)
                .unwrap_or_else(|| chrono::Utc::now()),
            CREATE_USER: Arc::from(hdfs_entity.CREATE_USER.as_str()),
            UPLOAD_TIME: chrono::DateTime::from_timestamp_millis(hdfs_entity.UPLOAD_TIME)
                .unwrap_or_else(|| chrono::Utc::now()),
            VERSION: hdfs_entity.VERSION,
            IS_DELETE: 0,
        }
    }
}
