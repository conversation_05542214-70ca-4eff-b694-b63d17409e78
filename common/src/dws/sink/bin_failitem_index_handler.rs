
use crate::ck::ck_sink::<PERSON><PERSON><PERSON><PERSON><PERSON>;

pub struct BinFailitemIndexHandler {
    db_name: String,
    table_name: String,
}

impl BinFailitemIndexHandler {
    pub fn new(db_name: String) -> Self {
        Self {
            db_name,
            table_name: "dws_bin_failitem_index_local".to_string(),
        }
    }
}

impl SinkHandler for BinFailitemIndexHandler {
    fn db_name(&self) -> &str {
        &self.db_name
    }

    fn table_name(&self) -> &str {
        &self.table_name
    }

    fn partition_expr(&self) -> &str {
        "('{CUSTOMER}', '{UPLOAD_TYPE}', '{TEST_AREA}', '{FACTORY}', '{SUB_CUSTOMER}', '{DEVICE_ID}')"
    }
}
