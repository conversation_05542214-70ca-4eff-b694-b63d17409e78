//! TestItemIndex的ClickHouse Sink Handler实现

use crate::ck::ck_sink::SinkHand<PERSON>;
use crate::dws::model::TestItemIndex;

/// TestItemIndex的ClickHouse Sink Handler
/// 对应Scala版本的TestItemIndexHandler
pub struct TestItemIndexHandler {
    db_name: String,
    table_name: String,
}

impl TestItemIndexHandler {
    pub fn new(db_name: String) -> Self {
        Self {
            db_name,
            table_name: "dws_test_item_index_local".to_string(),
        }
    }
}

impl SinkHandler for TestItemIndexHandler {
    fn db_name(&self) -> &str {
        &self.db_name
    }

    fn table_name(&self) -> &str {
        &self.table_name
    }

    fn partition_expr(&self) -> &str {
        // 与Scala版本保持一致的分区表达式
        "('{CUSTOMER}', '{UPLOAD_TYPE}', '{TEST_AREA}', '{FACTORY}', '{SUB_CUSTOMER}', '{DEVICE_ID}')"
    }
}
