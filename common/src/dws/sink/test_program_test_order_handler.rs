
use crate::ck::ck_sink::<PERSON><PERSON><PERSON><PERSON><PERSON>;

pub struct TestProgramTestOrderHandler {
    db_name: String,
    table_name: String,
}

impl TestProgramTestOrderHandler {
    pub fn new(db_name: String) -> Self {
        Self {
            db_name,
            table_name: "dim_test_program_test_order_local".to_string(),
        }
    }
}

impl SinkHandler for TestProgramTestOrderHandler {
    fn db_name(&self) -> &str {
        &self.db_name
    }

    fn table_name(&self) -> &str {
        &self.table_name
    }

    fn partition_expr(&self) -> &str {
        "('{CUSTOMER}', '{UPLOAD_TYPE}', '{TEST_AREA}', '{FACTORY}', '{SUB_CUSTOMER}')"
    }
}
