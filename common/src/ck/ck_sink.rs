use ck_provider::{write_to_ck_parallel, <PERSON>k<PERSON>on<PERSON><PERSON>, <PERSON>k<PERSON><PERSON><PERSON>, CkProviderImpl};
use clickhouse::Row;
use serde::Serialize;
use std::error::Error;

/// Base trait for sink handlers without type parameters
/// This allows for dynamic dispatch when we only need basic handler information
pub trait SinkHandler {
    fn db_name(&self) -> &str;
    fn table_name(&self) -> &str;
    fn partition_expr(&self) -> &str;
}

/// CkSink provides ClickHouse sink operations
/// Corresponds to CkSink object in Scala
pub struct CkSink;

impl CkSink {
    pub async fn write_to_ck_with_partition<T>(
        data: &[T],
        num_partitions: &str,
        ck_param: &CkConfig,
        handler: &impl SinkHandler,
        partition: &str,
    ) -> Result<(), Box<dyn Error>>
    where
        T: Row + Serialize + Send + Sync + Clone + 'static,
    {
        // Parse num_partitions
        let num_partitions_usize = num_partitions.parse::<usize>().unwrap_or(1);

        // Write data to ClickHouse
        CkSink::write_to_ck(data, num_partitions_usize, ck_param, handler, false).await?;

        // Optimize table
        let optimize_sql = Self::get_optimize_table_sql(handler, partition);
        CkSink::optimize_table(ck_param, &optimize_sql).await?;

        Ok(())
    }

    pub async fn write_to_ck_with_partition_from_config<T>(
        data: &[T],
        num_partitions: &str,
        ck_config: CkConfig,
        handler: &impl SinkHandler,
        partition: &str,
    ) -> Result<(), Box<dyn Error>>
    where
        T: Row + Serialize + Send + Sync + Clone + 'static,
    {
        // Call the main method
        Self::write_to_ck_with_partition(data, num_partitions, &ck_config, handler, partition).await
    }

    pub async fn write_to_ck<T>(
        data: &[T],
        num_partitions: usize,
        ck_param: &CkConfig,
        handler: &impl SinkHandler,
        _is_batch: bool,
    ) -> Result<(), Box<dyn Error>>
    where
        T: Row + Serialize + Send + Sync + Clone + 'static,
    {
        if data.is_empty() {
            return Ok(());
        }

        // Create ClickHouse provider
        let provider = CkProviderImpl::new(ck_param.clone());

        // Get full table name
        let table_name = format!("{}.{}", handler.db_name(), handler.table_name());

        // Write data using parallel writing if num_partitions > 1
        if num_partitions > 1 {
            write_to_ck_parallel(&provider, &table_name, data, num_partitions)
                .await
                .map_err(|e| Box::new(e) as Box<dyn Error>)?;
        } else {
            provider.insert(&table_name, data).await.map_err(|e| Box::new(e) as Box<dyn Error>)?;
        }

        Ok(())
    }

    pub async fn optimize_table(config: &CkConfig, optimize_sql: &str) -> Result<(), Box<dyn Error>> {
        // Create ClickHouse provider
        let provider = CkProviderImpl::new(config.clone());

        // Execute optimization SQL
        provider.execute(optimize_sql).await.map_err(|e| Box::new(e) as Box<dyn Error>)?;

        Ok(())
    }

    pub fn get_optimize_table_sql(handler: &impl SinkHandler, partition: &str) -> String {
        // Generate OPTIMIZE TABLE SQL for the specific partition
        // This corresponds to the Scala implementation that generates optimization SQL
        format!("OPTIMIZE TABLE {}.{} PARTITION {}", handler.db_name(), handler.table_name(), partition)
    }
}
