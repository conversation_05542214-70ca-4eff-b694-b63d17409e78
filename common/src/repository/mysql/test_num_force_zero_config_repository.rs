use mysql_provider::{MySqlConfig, MySqlProvider, MySqlProviderError, MySqlProviderImpl};
use sqlx::FromRow;

pub struct TestNumForceZeroConfigRepository<'a> {
    mysql_provider: &'a MySqlProviderImpl,
}

#[derive(Debug, FromRow)]
pub struct TestNumForceZeroTestProgram {
    pub customer: String,
    pub test_program: String,
}

impl<'a> TestNumForceZeroConfigRepository<'a> {
    pub async fn new(mysql_provider: &'a MySqlProviderImpl) -> Result<Self, MySqlProviderError> {
        Ok(Self { mysql_provider })
    }

    pub async fn read_test_num_force_zero_test_program_list(
        &self,
        customer: String,
        sub_customer: String,
        factory: String,
        factory_site: String,
        test_area: String,
        device_id: String,
        test_stage: String,
        test_program_list: Vec<String>,
        upload_type: String,
    ) -> Result<Vec<String>, MySqlProviderError> {
        let sql = format!(
            "SELECT customer, test_program FROM dw_test_num_force_zero_config WHERE customer = '{}'
            AND (sub_customer = '{}' OR sub_customer IS NULL OR sub_customer = '')
            AND (factory = '{}' OR factory IS NULL OR factory = '')
            AND (factory_site = '{}' OR factory_site IS NULL OR factory_site = '')
            AND (test_area = '{}' OR test_area IS NULL OR test_area = '')
            AND (device_id = '{}' OR device_id IS NULL OR device_id = '')
            AND (test_stage = '{}' OR test_stage IS NULL OR test_stage = '')
            AND (upload_type = '{}' OR upload_type IS NULL OR upload_type = '')
            AND status = '1'
            AND delete_flag = '0' GROUP BY customer, test_program",
            customer, sub_customer, factory, factory_site, test_area, device_id, test_stage, upload_type
        );

        log::info!("countTestNumForceZeroConfig 执行sql：{}", sql);
        let db_test_program_list = self
            .mysql_provider
            .query::<TestNumForceZeroTestProgram>(&sql)
            .await?
            .iter()
            .map(|test_program| test_program.test_program.clone())
            .collect::<Vec<String>>();

        let test_num_force_zero_test_program = if test_program_list.is_empty() {
            test_program_list
        } else {
            db_test_program_list
                .iter()
                .filter(|test_program| test_program_list.contains(test_program))
                .map(|test_program| test_program.clone())
                .collect::<Vec<String>>()
        };
        log::info!(
            "testArea={}, deviceId={},testStage={} testNum赋值0的测试程序：{:?}",
            test_area,
            device_id,
            test_stage,
            test_num_force_zero_test_program
        );
        Ok(test_num_force_zero_test_program)
    }
}
