use mysql_provider::{MySqlConfig, MySqlProvider, MySqlProviderError, MySqlProviderImpl};
use crate::{dws::dws_service::DwsService, model::constant::{SUPPORT_TEST_AREA_CP, SUPPORT_TEST_AREA_CP_INKLESS_MAP, SUPPORT_TEST_AREA_CP_MAP}};

/// SFTP File Detail Repository
/// Corresponds to: SftpFileDetailRepository.scala
pub struct SftpFileDetailRepository {
    mysql_provider: MySqlProviderImpl,
}

impl SftpFileDetailRepository {
    /// Create new repository instance
    /// Corresponds to: SftpFileDetailRepository constructor in Scala
    pub async fn new(mysql_config: MySqlConfig) -> Result<Self, MySqlProviderError> {
        let mysql_provider = MySqlProviderImpl::new(mysql_config).await?;
        Ok(Self { mysql_provider })
    }

    /// Count lot wafer file size from dw_sftp_file_detail table
    /// Corresponds to: countLotWaferFileSize method in Scala
    pub async fn count_lot_wafer_file_size(
        &self,
        customer: &str,
        factory: &str,
        factory_site: &str,
        test_area: &str,
        device_id: &str,
        lot_id: &str,
        wafer_no: &str,
        test_stage: &str,
        lot_type: &str,
        file_category: &str,
    ) -> Result<i64, MySqlProviderError> {
        let wafer_condition = if DwsService::is_cp_test_area(test_area) {
            format!("AND b.wafer_no = '{}'", wafer_no)
        } else {
            String::new()
        };

        let sql = format!(
            r#"
            SELECT CAST(COALESCE(SUM(a.origin_file_size), 0) AS SIGNED) AS origin_file_size
            FROM dw_sftp_file_detail a
            INNER JOIN dw_lot_stocking_detail b
                ON b.file_name = a.local_file_name
                AND b.customer = '{}'
                AND b.factory = '{}'
                AND b.factory_site = '{}'
                AND b.test_area = '{}'
                AND b.lot_id = '{}'
                AND b.device_id = '{}'
                AND b.test_stage = '{}'
                AND b.lot_type = '{}'
                AND b.file_category = '{}'
                {}
            "#,
            customer,
            factory,
            factory_site,
            test_area,
            lot_id,
            device_id,
            test_stage,
            lot_type,
            file_category,
            wafer_condition
        );

        log::info!("查询条件：{}", sql);
        
        let count = self.mysql_provider.count(&sql).await?;
        Ok(count.unwrap_or(0))
    }
}