//! DW表仓库
//!
//! 提供DW表相关的数据库操作

use crate::dws::model::mysql::dw_table::DwTable;
use crate::model::constant::upload_type::UploadType;
use mysql_provider::{MySqlConfig, MySqlProvider, MySqlProviderError, MySqlProviderImpl};
use std::collections::HashSet;

/// DW表仓库
pub struct DwTableRepository {
    mysql_provider: MySqlProviderImpl,
}

impl DwTableRepository {
    /// 创建新的DwTableRepository实例
    pub async fn new(mysql_config: MySqlConfig) -> Result<Self, MySqlProviderError> {
        let mysql_provider = MySqlProviderImpl::new(mysql_config).await?;
        Ok(Self { mysql_provider })
    }

    /// 查询所有需要计算的表
    /// 
    /// # 参数
    /// * `upload_type` - 上传类型，默认为AUTO
    /// * `manual_type` - 手动类型，可选
    /// 
    /// # 返回
    /// 返回需要计算的表名集合
    pub async fn find_all_calculate_table(
        &self,
        upload_type: Option<UploadType>,
        manual_type: Option<&str>,
    ) -> Result<HashSet<String>, MySqlProviderError> {
        let upload_type = upload_type.unwrap_or(UploadType::AUTO);
        let manual_type_str = manual_type.unwrap_or("");

        let sql = format!(
            r#"
            SELECT CAST(source_table AS CHAR) as name
            FROM dw_table_lineage
            WHERE `table` IN (
                SELECT alias_name 
                FROM dw_table 
                WHERE calculate_flag = 1 
                AND upload_type = '{}' 
                AND manual_type = '{}'
            )
            AND source_table IS NOT NULL
            UNION
            SELECT CAST(alias_name AS CHAR) as name
            FROM dw_table
            WHERE calculate_flag = 1
            AND upload_type = '{}'
            AND manual_type = '{}'
            "#,
            upload_type.to_string(),
            manual_type_str,
            upload_type.to_string(),
            manual_type_str
        );

        log::info!("执行查询需要计算的表SQL: {}", sql);

        let dw_tables: Vec<DwTable> = self.mysql_provider.query(&sql).await?;

        log::info!("查询到的dwTable个数: {}", dw_tables.len());

        let table_names: HashSet<String> = dw_tables
            .into_iter()
            .map(|table| table.name)
            .collect();

        Ok(table_names)
    }

}
