use mysql_provider::{MySqlConfig, MySqlProvider, MySqlProviderError, MySqlProviderImpl};
use chrono::{DateTime, Local, NaiveDateTime, TimeZone, Utc};

use crate::dws::model::mysql::dw_table_calculate_record::DwTableCalculateRecord;

/// DW Table Calculate Record Repository
/// Corresponds to: DwTableCalculateRecordRepository.scala
pub struct DwTableCalculateRecordRepository {
    mysql_provider: MySqlProviderImpl,
}

impl DwTableCalculateRecordRepository {
    /// Create new repository instance
    /// Corresponds to: DwTableCalculateRecordRepository constructor in Scala
    pub async fn new(mysql_config: MySqlConfig) -> Result<Self, MySqlProviderError> {
        let mysql_provider = MySqlProviderImpl::new(mysql_config).await?;
        Ok(Self { mysql_provider })
    }

    /// Insert DW table calculate record
    /// Corresponds to: insertDwTableCalculateRecord method in Scala
    pub async fn insert_dw_table_calculate_record(
        &self,
        record: &DwTableCalculateRecord,
    ) -> Result<(), MySqlProviderError> {
        // Convert timestamps to formatted strings
        let calculate_start_time = self.format_timestamp(record.calculate_start_time);
        let calculate_end_time = self.format_timestamp(record.calculate_end_time);
        
        let sink_start_time = if let Some(time) = record.sink_ck_start_time {
            self.format_timestamp(Some(time))
        } else {
            calculate_end_time.clone()
        };
        
        let sink_end_time = if let Some(time) = record.sink_ck_end_time {
            self.format_timestamp(Some(time))
        } else {
            calculate_end_time.clone()
        };

        let sql = format!(
            r#"
            INSERT INTO dw_table_calculate_record
            (customer,
            sub_customer,
            upload_type,
            file_id,
            file_name,
            test_area,
            factory,
            factory_site,
            device_id,
            lot_id,
            wafer_no,
            file_category,
            lot_type,
            test_stage,
            dw_layer,
            db,
            local_name,
            cluster_name,
            calculate_start_time,
            calculate_end_time,
            calculate_time_diff,
            sink_ck_start_time,
            sink_ck_end_time,
            sink_ck_time_diff,
            file_size,
            die_data_count,
            test_item_data_count,
            warehousing_mode,
            run_mode,
            layer_calculate_pool_id,
            num_executors,
            executor_cores,
            executor_memory,
            driver_memory,
            parallelism,
            extra_conf,
            create_time,
            update_time,
            create_user,
            update_user
            )
            VALUES (
            '{}',
            '{}',
            '{}',
            {},
            '{}',
            '{}',
            '{}',
            '{}',
            '{}',
            '{}',
            '{}',
            '{}',
            '{}',
            '{}',
            '{}',
            '{}',
            '{}',
            '{}',
            '{}',
            '{}',
            {},
            '{}',
            '{}',
            {},
            {},
            {},
            {},
            '{}',
            '{}',
            {},
            {},
            {},
            {},
            {},
            {},
            '{}',
            NOW(),
            NOW(),
            'SYSTEM',
            'SYSTEM'
            )
            "#,
            record.customer.as_ref().unwrap_or(&String::new()),
            record.sub_customer.as_ref().unwrap_or(&String::new()),
            record.upload_type.as_ref().unwrap_or(&String::new()),
            record.file_id.unwrap_or(0),
            record.file_name.as_ref().unwrap_or(&String::new()),
            record.test_area.as_ref().unwrap_or(&String::new()),
            record.factory.as_ref().unwrap_or(&String::new()),
            record.factory_site.as_ref().unwrap_or(&String::new()),
            record.device_id.as_ref().unwrap_or(&String::new()),
            record.lot_id.as_ref().unwrap_or(&String::new()),
            record.wafer_no.as_ref().unwrap_or(&String::new()),
            record.file_category.as_ref().unwrap_or(&String::new()),
            record.lot_type.as_ref().unwrap_or(&String::new()),
            record.test_stage.as_ref().unwrap_or(&String::new()),
            record.dw_layer.as_ref().unwrap_or(&String::new()),
            record.db.as_ref().unwrap_or(&String::new()),
            record.local_name.as_ref().unwrap_or(&String::new()),
            record.cluster_name.as_ref().unwrap_or(&String::new()),
            calculate_start_time,
            calculate_end_time,
            record.calculate_time_diff.unwrap_or(0),
            sink_start_time,
            sink_end_time,
            record.sink_ck_time_diff.unwrap_or(0),
            record.file_size.unwrap_or(0),
            record.die_data_count.unwrap_or(0),
            record.test_item_data_count.unwrap_or(0),
            record.warehousing_mode.as_ref().unwrap_or(&String::new()),
            record.run_mode.as_ref().unwrap_or(&String::new()),
            record.layer_calculate_pool_id.unwrap_or(0),
            record.num_executors.unwrap_or(0),
            record.executor_cores.unwrap_or(0),
            record.executor_memory.unwrap_or(0),
            record.driver_memory.unwrap_or(0),
            record.parallelism.unwrap_or(0),
            record.extra_conf.as_ref().unwrap_or(&String::new()),
        );

        self.mysql_provider.execute(&sql).await?;
        Ok(())
    }

    /// Format timestamp to MySQL datetime string
    /// Corresponds to: dateTimeFormatter.format in Scala
    fn format_timestamp(&self, timestamp_opt: Option<i64>) -> String {
        match timestamp_opt {
            Some(timestamp) => {
                let dt = DateTime::<Utc>::from_utc(
                    NaiveDateTime::from_timestamp_millis(timestamp).unwrap_or_default(),
                    Utc,
                );
                let local_dt: DateTime<Local> = dt.with_timezone(&Local);
                local_dt.format("%Y-%m-%d %H:%M:%S").to_string()
            }
            None => {
                let now: DateTime<Local> = Local::now();
                now.format("%Y-%m-%d %H:%M:%S").to_string()
            }
        }
    }
}