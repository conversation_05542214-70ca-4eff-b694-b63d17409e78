use ck_provider::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>k<PERSON><PERSON><PERSON>E<PERSON>r, CkProviderImpl};
use clickhouse::Row;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use log::{info, error};
use crate::model::key::lot_key::LotKey;

/// Test program count result structure
#[derive(Row, Debug, Clone, Serialize, Deserialize, PartialEq)]
#[allow(non_snake_case)]
pub struct TestProgramCnt {
    pub TEST_PROGRAM: String,
    pub cnt: i64,
}

/// TestProgramTestOrderRepository for ClickHouse operations
pub struct TestProgramTestOrderRepository {
    ck_provider: CkProviderImpl,
    db_name: String,
}

impl TestProgramTestOrderRepository {
    /// Create a new TestProgramTestOrderRepository instance
    pub fn new(db_name: String, ck_config: CkConfig) -> Self {
        let ck_provider = CkProviderImpl::new(ck_config);
        Self {
            ck_provider,
            db_name,
        }
    }

    /// Check which test programs can initialize test order
    /// Returns test programs that have no existing records (count = 0)
    pub async fn can_init_test_program_test_order(
        &self,
        lot_key: &LotKey,
        upload_type: &str,
        test_programs: &[String],
    ) -> Result<Vec<String>, CkProviderError> {
        if test_programs.is_empty() {
            return Ok(Vec::new());
        }

        // Build the test programs list for SQL IN clause
        let test_programs_sql = test_programs
            .iter()
            .map(|tp| format!("'{}'", tp))
            .collect::<Vec<_>>()
            .join(",");

        let sql = format!(
            r#"
            SELECT TEST_PROGRAM, count(1) as cnt
            FROM {}.dim_test_program_test_order_cluster
            WHERE CUSTOMER = '{}'
              AND SUB_CUSTOMER = '{}'
              AND UPLOAD_TYPE = '{}'
              AND TEST_AREA = '{}'
              AND FACTORY = '{}'
              AND FACTORY_SITE = '{}'
              AND DEVICE_ID = '{}'
              AND TEST_STAGE = '{}'
              AND LOT_TYPE = '{}'
              AND IS_DELETE = 0
              AND TEST_PROGRAM IN ({})
            GROUP BY TEST_PROGRAM
            "#,
            self.db_name,
            lot_key.customer,
            lot_key.sub_customer,
            upload_type,
            lot_key.test_area,
            lot_key.factory,
            lot_key.factory_site,
            lot_key.device_id,
            lot_key.test_stage,
            lot_key.lot_type,
            test_programs_sql
        );

        info!("Executing SQL: {}", sql);

        // Execute query to get existing test program counts
        let results: Vec<TestProgramCnt> = self.ck_provider.query(&sql).await?;

        // Create a map of test program -> count
        let count_map: HashMap<String, i64> = results
            .into_iter()
            .map(|item| (item.TEST_PROGRAM, item.cnt))
            .collect();

        // Filter test programs that have count = 0 (can be initialized)
        let can_init_programs: Vec<String> = test_programs
            .iter()
            .filter(|tp| count_map.get(*tp).unwrap_or(&0) == &0)
            .cloned()
            .collect();

        info!(
            "Test programs that can be initialized: {:?}",
            can_init_programs
        );

        Ok(can_init_programs)
    }

}
